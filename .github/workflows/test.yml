name: Run tests

on:
  pull_request:
  push:

jobs:
  check-lockfile:
    name: check lockfile correctness
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
      - name: Check that lockfile does not need an update
        run: pnpm install --frozen-lockfile

  test-web:
    name: web sanity check
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 'lts/*'
      - uses: pnpm/action-setup@v4
      - run: .github/test.sh web
        env:
          WEB_DEFAULT_API: https://api.dummy.example/

  test-api:
    name: api sanity check
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
      - run: .github/test.sh api
