:root {
    --primary: #ffffff;
    --secondary: #000000;

    --white: #ffffff;
    --gray: #75757e;

    --red: #ed2236;
    --medium-red: #ce3030;
    --dark-red: #d61c2e;
    --green: #30bd1b;
    --blue: #2f8af9;
    --magenta: #eb445a;
    --purple: #5857d4;
    --orange: #f19a38;

    --focus-ring: solid 2px var(--blue);
    --focus-ring-offset: -2px;

    --button: #f4f4f4;
    --button-hover: #ededed;
    --button-press: #e8e8e8;
    --button-active-hover: #2a2a2a;

    --button-hover-transparent: rgba(0, 0, 0, 0.06);
    --button-press-transparent: rgba(0, 0, 0, 0.09);
    --button-stroke: rgba(0, 0, 0, 0.06);
    --button-text: #282828;
    --button-box-shadow: 0 0 0 1px var(--button-stroke) inset;

    --button-elevated: #e3e3e3;
    --button-elevated-hover: #dadada;
    --button-elevated-press: #d3d3d3;
    --button-elevated-shimmer: #ededed;

    --popover-glow: var(--button-stroke);

    --popup-bg: #f1f1f1;
    --popup-stroke: rgba(0, 0, 0, 0.08);

    --dialog-backdrop: rgba(255, 255, 255, 0.3);

    --sidebar-bg: var(--button);
    --sidebar-highlight: var(--secondary);
    --sidebar-stroke: rgba(0, 0, 0, 0.04);

    --content-border: rgba(0, 0, 0, 0.03);
    --content-border-thickness: 1px;

    --input-border: #adadb7;

    --toggle-bg: var(--input-border);
    --toggle-bg-enabled: var(--secondary);

    --padding: 12px;
    --border-radius: 11px;

    --sidebar-width: 80px;
    --sidebar-font-size: 11px;
    --sidebar-inner-padding: 4px;
    --sidebar-tab-padding: 10px;

    /* reduce default inset by 5px if it's not 0 */
    --sidebar-height-mobile: calc(
        50px +
            calc(
                env(safe-area-inset-bottom) - 5px *
                    sign(env(safe-area-inset-bottom))
            )
    );

    --safe-area-inset-top: env(safe-area-inset-top);
    --safe-area-inset-bottom: env(safe-area-inset-bottom);

    --switcher-padding: 3.5px;

    /* used for fading the tab bar on scroll */
    --sidebar-mobile-gradient: linear-gradient(
        90deg,
        rgba(0, 0, 0, 0.9) 0%,
        rgba(0, 0, 0, 0) 5%,
        rgba(0, 0, 0, 0) 50%,
        rgba(0, 0, 0, 0) 95%,
        rgba(0, 0, 0, 0.9) 100%
    );

    --skeleton-gradient: linear-gradient(
        90deg,
        var(--button-hover),
        var(--button),
        var(--button-hover)
    );

    --skeleton-gradient-elevated: linear-gradient(
        90deg,
        var(--button-elevated),
        var(--button-elevated-shimmer),
        var(--button-elevated)
    );
}

[data-theme="dark"] {
    --primary: #000000;
    --secondary: #e1e1e1;

    --gray: #818181;

    --blue: #2a7ce1;
    --green: #37aa42;

    --button: #191919;
    --button-hover: #242424;
    --button-press: #2a2a2a;

    --button-active-hover: #f9f9f9;

    --button-hover-transparent: rgba(225, 225, 225, 0.1);
    --button-press-transparent: rgba(225, 225, 225, 0.15);
    --button-stroke: rgba(255, 255, 255, 0.05);
    --button-text: #e1e1e1;
    --button-box-shadow: 0 0 0 1px var(--button-stroke) inset;

    --button-elevated: #282828;
    --button-elevated-hover: #2f2f2f;
    --button-elevated-press: #343434;

    --popover-glow: rgba(135, 135, 135, 0.12);

    --popup-bg: #191919;
    --popup-stroke: rgba(255, 255, 255, 0.08);

    --dialog-backdrop: rgba(0, 0, 0, 0.3);

    --sidebar-bg: #131313;
    --sidebar-highlight: var(--secondary);
    --sidebar-stroke: rgba(255, 255, 255, 0.04);

    --content-border: rgba(255, 255, 255, 0.045);

    --input-border: #383838;

    --toggle-bg: var(--input-border);
    --toggle-bg-enabled: #8a8a8a;

    --sidebar-mobile-gradient: linear-gradient(
        90deg,
        rgba(19, 19, 19, 0.9) 0%,
        rgba(19, 19, 19, 0) 5%,
        rgba(19, 19, 19, 0) 50%,
        rgba(19, 19, 19, 0) 95%,
        rgba(19, 19, 19, 0.9) 100%
    );

    --skeleton-gradient: linear-gradient(
        90deg,
        var(--button),
        var(--button-hover),
        var(--button)
    );

    --skeleton-gradient-elevated: linear-gradient(
        90deg,
        var(--button-elevated),
        var(--button-elevated-hover),
        var(--button-elevated)
    );
}

/* fall back to less pretty value cuz chrome doesn't support sign() */
[data-chrome="true"] {
    --sidebar-height-mobile: calc(50px + env(safe-area-inset-bottom));
}

[data-theme="light"] [data-reduce-transparency="true"] {
    --dialog-backdrop: rgba(255, 255, 255, 0.6);
}

[data-theme="dark"] [data-reduce-transparency="true"] {
    --dialog-backdrop: rgba(0, 0, 0, 0.5);
}

html,
body {
    margin: 0;
    height: 100vh;
    overflow: hidden;
    overscroll-behavior-y: none;
}

* {
    font-family: "IBM Plex Mono", monospace;
    user-select: none;
    scrollbar-width: none;
    -webkit-user-select: none;
    -webkit-user-drag: none;
    -webkit-tap-highlight-color: transparent;
}

::-webkit-scrollbar {
    display: none;
}

::selection {
    color: var(--primary);
    background: var(--secondary);
}

a {
    color: inherit;
    text-underline-offset: 3px;
    -webkit-touch-callout: none;
}

a:visited {
    color: inherit;
}

svg,
img {
    pointer-events: none;
}

button, .button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 6px 13px;
    gap: 6px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 14.5px;
    cursor: pointer;
    background-color: var(--button);
    color: var(--button-text);
    box-shadow: var(--button-box-shadow);
}

:focus-visible {
    outline: none;
}

button:focus-visible,
a:focus-visible,
select:focus-visible {
    outline: var(--focus-ring);
    outline-offset: var(--focus-ring-offset);
}

a:not(.sidebar-tab):not(.subnav-tab):focus-visible {
    outline-offset: 3px;
    border-radius: 2px;
}

.button.elevated {
    background-color: var(--button-elevated);
}

.button.active {
    color: var(--primary);
    background-color: var(--secondary);
}

/* important is used because active class is toggled by state */
/* and added to the end of the list, taking priority */
.button.active:focus-visible,
a.active:focus-visible {
    color: var(--white) !important;
    background-color: var(--blue) !important;
}

@media (hover: hover) {
    .button:hover {
        background-color: var(--button-hover);
    }

    .button.elevated:not(.color):hover {
        background-color: var(--button-elevated-hover);
    }

    .button.active:not(.color):hover {
        background-color: var(--button-active-hover);
    }
}

.button:active {
    background-color: var(--button-press);
}

.button.elevated:not(.color):active {
    background-color: var(--button-elevated-press);
}

.button.elevated {
    box-shadow: none;
}

.button.active:not(.color):active {
    background-color: var(--button-active-hover);
}

button[disabled] {
    cursor: default;
}

/* workaround for typing into inputs being ignored on iPadOS 15 */
input {
    user-select: text;
    -webkit-user-select: text;
}

.center-column-container {
    display: flex;
    width: 100%;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

button {
    font-weight: 500;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 500;
    margin-block: 0;
}

h1 {
    font-size: 24px;
    letter-spacing: -1px;
}

h2 {
    font-size: 20px;
    letter-spacing: -1px;
}

h3 {
    font-size: 16px;
}

h4 {
    font-size: 14.5px;
}

h5 {
    font-size: 12px;
}

h6 {
    font-size: 11px;
}

.subtext {
    font-size: 12.5px;
    font-weight: 500;
    color: var(--gray);
    line-height: 1.4;
    padding: 0 var(--padding);
    white-space: pre-line;
    user-select: text;
    -webkit-user-select: text;
}

.long-text,
.long-text *:not(h1, h2, h3, h4, h5, h6) {
    line-height: 1.8;
    font-size: 14.5px;
    font-family: "IBM Plex Mono", monospace;
    user-select: text;
    -webkit-user-select: text;
}

.long-text,
.long-text *:not(h1, h2, h3, h4, h5, h6, strong, em, del) {
    font-weight: 400;
}

.long-text ul {
    padding-inline-start: 30px;
}

.long-text li {
    padding-left: 3px;
}

.long-text:not(.about) h1,
.long-text:not(.about) h2,
.long-text:not(.about) h3 {
    user-select: text;
    -webkit-user-select: text;
    letter-spacing: 0;
    margin-block-start: 1rem;
}

.long-text h3 {
    font-size: 17px;
}

.long-text h2 {
    font-size: 19px;
}

.long-text:not(.about) h3 {
    margin-block-end: -0.5rem;
}

.long-text:not(.about) h2 {
    font-size: 19px;
    line-height: 1.3;
    margin-block-end: -0.3rem;
    padding: 6px 0;
    border-bottom: 1.5px solid var(--button-elevated-hover);
}

.long-text img {
    border-radius: 6px;
}

table,
td,
th {
    border-spacing: 0;
    border-style: solid;
    border-width: 1px;
    border-collapse: collapse;
    text-align: center;
    padding: 3px 8px;
}

code {
    background: var(--button-elevated);
    padding: 1px 4px;
    border-radius: 4px;
}

tr td:first-child,
tr th:first-child {
    text-align: right;
}

.long-text.about section p:first-of-type {
    margin-block-start: 0.3em;
}

.long-text.about .heading-container {
    padding-top: calc(var(--padding) / 2);
}

.long-text.about section:first-of-type .heading-container {
    padding-top: 0;
}


@media screen and (max-width: 535px) {
    .long-text,
    .long-text *:not(h1, h2, h3, h4, h5, h6) {
        font-size: 14px;
    }
}

[data-reduce-motion="true"] * {
    animation: none !important;
    transition: none !important;
}

@keyframes spinner {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
