---
title: "squashing bugs, improving security and ux"
date: "1 Oct, 2024"
banner:
    file: "meowth101hammer.webp"
    alt: "meowth plush getting squished with a hammer."
---

this update enhances the cobalt experience all around, here's everything that we added or changed since 10.0:

### saving improvements:
- youtube videos encoded in av1 are now downloaded in the webm container. they also include opus audio for the best quality all around.
- fixed various bugs related to the download process on older devices/browsers. cobalt should work everywhere within sane limits.
- fixed downloading of twitch clips.
- fixed a bug where cobalt wouldn't download bluesky videos that are in a post with a quote.
- fixed a bug that caused some youtube music videos to fail to download due to differently formatted metadata.
- cobalt will no longer unexpectedly open video files on iOS. instead, a dialog with other options will be shown. this had to be done due to missing "download" button in safari's video player. you can override this by enabling [forced tunneling](/settings/privacy#tunnel).
- fixed a bug in filename generation where certain information was added to the filename even if cobalt didn't have it (such as youtube video format).

### general ui/ux improvements:
- added a button to quickly copy a link to the section in settings or about page.
- added `(remux)` to filenames of remuxed videos to distinguish them from the original file.
- improved the look & behavior of the sidebar.
- fixed cursor appearance to update correctly when using the sidebar or subpage navigation.
- added a stepped scroller to the donation options card [on the donate page](/donate).
- tweaked the [donate page](/donate) layout to be cleaner and more flexible.
- fixed tab navigation for donation option buttons.
- updated the [10.0 changelog banner](/updates#10.0) to be less boring.
- fixed a bug that caused some changelog dates to be displayed a day later than intended.
- changelog banner can now be saved with a right click.
- cobalt version now gently fades in on the [settings page](/settings).
- fixed the position of the notch easter egg on iPhone XR, 11, 16 Pro, and 16 Pro Max.
- cobalt will let you paste the link even if the anti-bot check isn't completed yet. if anything goes wrong regarding anti-bot checks, cobalt will let you know.
- fixed a bunch of typos and minor grammatical errors.
- other minor changes.

### about page improvements:
- added motivation section to the [general about page](/about/general).
- added a list of beta testers to the [credits page](/about/credits).
- rephrased some about sections to improve clarity and readability.
- made about page body narrower to be easier to read.
- added extra padding between sections on about page to increase readability.

### internal improvements:
- cobalt now preloads server info for quicker access to supported services & loading turnstile on demand.
- converted all elements and the about page to be translatable in preparations for community-sourced translations *(coming soon!)*.
- added `content-security-policy` header to restrict and better prevent XSS attacks.
- moved the turnstile bot check key to the server, making it load the script on the client only if necessary.
- fixed a bug in the api that allowed for making requests without a valid `Accept` header if authentication wasn't enabled on an instance.

you can also check [all commits since the 10.0 release on github](https://github.com/imputnet/cobalt/compare/08bc5022...f461b02f).

we hope you enjoy this stable update and have a wonderful day!

\~ your friends at imput ❤️
