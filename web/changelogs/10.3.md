---
title: "fastest cobalt yet, new youtube features, translation platform, and a lot more"
date: "4 Nov, 2024"
banner:
    file: "meowbalt_very_fast.webp"
    alt: "meowbalt absolutely zooming through space and time (only me<PERSON><PERSON> and his speed trail are pictured)."
---

## oh-so-fast
starting from this update, cobalt can run several instances in parallel, reducing load on individual instances and making it much faster.
previously cobalt ran on *only one thread*, and it's honestly impressive that it lasted this long.

we tested cobalt under peak traffic load & same network conditions:
- initial request processing is now **~14 times faster than before**.
- starting a tunnel is now **~32 times faster**.

<div style="display: flex; justify-content: center;">

|                   | 10.2    | **10.3**   |
|-------------------|---------|------------|
| processing        | 14780ms | **1070ms** |
| starting a tunnel | 11660ms | **360ms**  |

</div>

these tests weren't really scientific as we based them on screen recordings,
but the point still stands: cobalt no longer slows down and runs as fast as it can.

## youtube improvements
- added a [new hls option](/settings/video#youtube-hls) that allows for downloading *more formats* of youtube videos.
- fixed an issue that caused long youtube videos to get abruptly cut off. if you still experience this issue, try enabling the [new hls option](/settings/video#youtube-hls) in settings!
- added an option to [pick any audio track language](/settings/audio#youtube-dub) for youtube videos in settings. all languages that youtube supports are listed, cobalt will fall back to default if preferred language isn't available.
- if a [youtube codec](/settings/video#youtube-codec) isn't available, cobalt will now fall back to the next best one.

## meet weblate, a place where you can translate cobalt
we're finally ready to invite you to translate cobalt to any language you like! your translation contributions are linked to your github account, so you'll show up in cobalt's contributors list.

you can start translating cobalt at [i18n.imput.net](https://i18n.imput.net/) right now!

thank you for showing such an overwhelming amount of interest in making cobalt more accessible around the world, we really appreciate it!

## other service improvements
- added support for bookmark links from twitter.
- fixed parsing of some mobile tiktok links.
- fixed twitter gifs having an incorrect extension in the content picker.
- fixed a bug that broke downloading older (shorter) links from streamable.
- fixed video downloading from odnoklassniki (ok.ru).

## ui/ux improvements
- [always-on file tunneling](/settings/privacy#tunnel) is out of beta! feel free to use it if your isp tracks or filters your internet traffic.
- redesigned the [community & support page](/about/community), added bluesky and removed support email.
- improved the debug page: added a button to copy data, added current states, fixed padding. if you're curious, it can be enabled in [advanced settings](/settings/advanced#debug).
- reduced timeouts on action buttons in security warning popups as they were very annoying before.
- added a message about cobalt not being fully usable without javascript when the page is loaded without it.
- improved contrast of all emoji icons on the home/save page.
- improved contrast of the toggle button.
- fixed the color of text selection, it's no longer hideous.
- audio bitrate section now gets greyed out when it's not applicable.
- fixed cursor state (pointer, arrow, etc) on various buttons.
- fixed a bug when iphone landscape mode optimizations were applied incorrectly (fix for a bug in ios firefox).
- various text/phrasing improvements across ui.
- small padding improvements across ui.
- other small improvements.

## documentation improvements
- all [documentation on github](https://github.com/imputnet/cobalt) was majorly improved. all projects and docs are now listed in the main readme. all docs are now easier to read and follow.
- added a new document outlining all [instance protection methods](https://github.com/imputnet/cobalt/blob/main/docs/protect-an-instance.md) along with step-by-step tutorials on how to configure them.
- added a tutorial for [configuring a cobalt instance for youtube downloading](https://github.com/imputnet/cobalt/blob/main/docs/configure-for-youtube.md).
- updated [contribution guidelines](https://github.com/imputnet/cobalt/blob/main/CONTRIBUTING.md).
- updated [examples](https://github.com/imputnet/cobalt/tree/main/docs/examples) for cookie & docker compose files. we now recommend running cobalt api as **read only** image, as it ensures that it wasn't tampered with. we do it on our servers, too.

## internal improvements for nerds
- added support for api keys, api instance hosters are now able to limit access to a set of people. you can see [how to configure them on github](https://github.com/imputnet/cobalt/blob/main/docs/protect-an-instance.md#configure-api-keys).
- cobalt api docker image is now running alpine & node 23. it's also much smaller than before.
- instances now log whether they were able to load cookies or api keys. no more guessing if your config works or not.
- updated the console error when cobalt api is configured incorrectly.
- majorly refactored the youtube module.
- lots of general api code refactoring.
- improved settings schema migration on frontend.
- removed outdated api functions, util scripts, and docs.

## fixed a XSS vulnerability that wasn't exploited
a malicious cobalt instance could serve links with the javascript: protocol, resulting in XSS when the user tries to download an item from a picker.

as far as we know, this vulnerability was never found and exploited in the wild, but we still urge all frontend instance hosters to **update their instances asap**. cobalt.tools and all other instances that configured CSP correctly weren't affected by this vulnerability.

this issue was fully fixed in [c4be1d3](https://github.com/imputnet/cobalt/commit/c4be1d3a37b0deb6b6087ec7a815262ac942daf1) and [an advisory with CVE was posted on github](https://github.com/imputnet/cobalt/security/advisories/GHSA-cm4c-v4cm-3735).

if you ever discover a security vulnerability in cobalt, please report it responsibly [on github](https://github.com/imputnet/cobalt/security/advisories/new). we'll make sure to fix it as soon as possible!

## where's 10.2?
we were very excited to release the first part of changes, so we bumped the version early. then, we decided to make cobalt faster, so now we're at 10.3!

*we also silently released changes in prod before the announcement, teehee :3c*

## all changes are on github
as always, you can check [all commits since the 10.1 release on github](https://github.com/imputnet/cobalt/compare/f461b02...c021293) for even more details.

we hope you enjoy this update as much as you enjoy fresh air, because it really feels like one!

\~ your friends at imput ❤️