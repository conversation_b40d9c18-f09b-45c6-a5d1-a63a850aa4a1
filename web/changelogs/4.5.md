---
title: "better, faster, stronger, stable"
date: "Dec 6, 2022"
banner:
    file: "meowthstrong.webp"
    alt: "meowth stretching"
---
your favorite social media downloader just got even better! this update includes a ton of improvements and fixes.

in fact, there are so many changes, i had to split them in sections.

service-related improvements:
- vimeo module has been revamped, all sorts of videos should now be supported.
- vimeo audio downloads! you now can download audios from more recent videos.
- cobalt now supports all sorts of tumblr links. (even those scary ones from the mobile app)
- vk clips support has been fixed. they rolled back the separation of videos and clips, so i had to do the same.
- youtube videos with community warnings should now be possible to download.
user interface improvements:
- list of supported services is now MUCH easier to read.
- banners in changelog history should no longer overlap each other.
- bullet points! they have a bit of extra padding, so it makes them stand out of the rest of text.
internal improvements:
- cobalt will now match the link to regex when using ?u= query for autopasting it into input area.
- better rate limiting: limiting now is done per minute, not per 20 minutes. this ensures less waiting and less attack area for request spammers.
- moved to my own fork of ytdl-core, cause main project seems to have been abandoned. go check it out on [github](https://github.com/wukko/better-ytdl-core) or [npm](https://www.npmjs.com/package/better-ytdl-core)!
- ALL user inputs are now properly sanitized on the server. that includes variables for POST api method, too.
- "got" package has been (mostly) replaced by native fetch api. this should greatly reduce ram usage.
- all unnecessary duplications of module imports have been gotten rid of. no more error passing strings from inside of service modules. you don't make mistakes only if you don't do anything, right?
- other code optimizations. there's less clutter overall.
huge update, right? seems like everything's fixed now?

nope, one issue still persists: sometimes youtube server drops packets for an audio file while cobalt's rendering the video for you. this results in abrupt cuts of audio. if you want to help solving this issue, [please feel free to do it on github!](https://github.com/imputnet/cobalt/issues/62)

thank you for reading this, and thank you for sticking with cobalt and me.