---
title: "beginning of 2.2"
date: "Jul 13, 2022"
---

- added download popup to solve the issue with downloads on ios
- merged big and small popups into one
- made buttons in donation menu act like buttons
- began to clean up localisation
- added ability to embed repo url into localisation strings
- moved ffmpeg args to config for more flexibility (and hopefully future changes)
- removed error response in stream that could result in a crash
- removed notice for ios users from about cause it's no longer relevant
- made error popup look and act like the rest
- a tiny bit of clean up
- changelog is now made out of latest commit (and doesn't break)