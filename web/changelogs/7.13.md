---
title: "better ux, improvements for youtube, twitter, tiktok, instagram, and more!"
date: "May 5, 2024"
banner:
    file: "meowthbusinessman.webp"
    alt: "photo of a businessman holding hands together (merkel-raute pose) with meowth plush head."
---
long time no see! well, actually, you've been using the latest version for some time now. we've moved to a rolling release scheme, allowing for speedy update rollouts :)

since 7.11, there has been a ton of changes. here are the most notable of them:
- youtube downloads are now faster and more reliable than ever.
- all posts from twitter are now downloadable, including sensitive ones.
- you now can download tiktok videos in 1080p h265! just enable h265 support in settings > video.
- added support for sharing links directly to the cobalt web app on android.
- added 240p and 144p quality options to the quality picker in settings (for some reason, many of you wanted this).
- pasting a link with additional text around it will now work; cobalt will extract the link for you (works only via the paste button).
- added anonymous traffic analytics by plausible. we're using a selfhosted instance and don't collect any identifiable information about you. you can learn more in about > privacy policy. you can also opt out of anonymous analytics in settings > other.

service support improvements:
- implemented internal streams functionality, allowing for more fine-grained file streaming and therefore proper youtube support.
- added fallback to m4a if opus isn't available for youtube.
- added a total of 7 ways to get instagram post info, including mobile api, embed, and graphql api. absolute torture.
- added support for reddit user posts.
- updated the way tiktok downloads are handled for better reliability and 1080p support.
- added tiktok author's username to filename.
- added support for rutube shorts and yappy videos.
- added support for m.soundcloud.com links.
- added support for new post and reel links from instagram.
- added support for photo twitter links, only used for gifs.
- added support for m.bilibili.com links.
- added support for new type of vimeo links.
- added support for ddinstagram.com links.
- updated youtube codec info in settings to display the fact that av1 is a better choice now.
- updated best audio picking for tiktok and soundcloud.
- changed the youtube client to web, since android client no longer works.
- removed the vimeo download type switcher, as it should've always been automatic instead.
- removed an ability to enable the tiktok watermark, as it no longer includes the author's username.

ui & ux improvements:
- youtube audio dub switcher is now a toggle with a much easier to understand description.
- meowbalt now sticks out on the left side of download popup on desktop.
- updated "made with love" text to include the research & dev team behind cobalt, imput.
- fixed grammar of russian localization.
- rounded corners are now correctly rendered across all browsers.
- various minor improvements, including smaller button padding.
- removed the notification (red dot) functionality as the most recent changelog is already always on screen.
- removed settings migration from the old domain.

other changes:
- various docs updates in github repo, making sure they're functional across branches and forks.
- major codebase cleanup.

thank you for using cobalt, and thank you for being one of our 900k friends! i hope you like this update as much as we liked making it.

we're committed to keeping cobalt the best way to save what you love without ads or invasion of your privacy. there's a ton of cool stuff to come soon; stay tuned and have an amazing rest of your day &amp;lt;3

if you want to help our goal of a better internet for everyone, just share cobalt with a friend!

(original photo of a man in a suit by benzoix on freepik)