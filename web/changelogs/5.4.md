---
title: "instagram support, docker, and more!"
date: "Apr 24, 2023"
banner:
    file: "catphonestand.webp"
    alt: "a cat holding a phone under its chin while a person plays clash of clans on it"
---
something many of you've been waiting for is finally here! try it out and let me know what you think :)

<span class='text-backdrop'>tl;dr:</span>

- added experimental instagram support! download any reels or videos you like, and make sure to report any issues you encounter. yes, you can convert either to audio.
- fixed support for on.soundcloud links.
- added share button to "how to save?" popup.
- added docker support.

service improvements:
- added experimental support for videos from instagram. currently only reels and post videos are downloadable, but i'm looking into ways to save high resolution photos too. if you experience any issues, please report them on either of support platforms.
- fixed support for on.soundcloud share links. should work just as well as other versions!
- fixed an issue that made some youtube videos impossible to download.

interface improvements:
- new css-only checkmark! yes, i can't stop tinkering with it because slight flashing on svg load annoyed me. now it loads instantly (and also looks slightly better).
- fixed copy animation.
- minor localization improvements.
- fixed the embed logo that i broke somewhere in between 5.3 and 5.4.

internal improvements:
- now using nanoid for live render stream ids.
- added support for docker. it's kind of clumsy because of how i get .git folder inside the container, but if you know how to do it better, feel free to make a pr.
- cobalt now checks only for existence of environment variables, not exactly the .env file.
- changed the way user ip address is retrieved for instances using cloudflare.
- added ability to disable cors, both to setup script and environment variables.

i can't believe how diverse and widespread cobalt has become. it's used in all fields: music production, education, content creation, and even game development. <span class='text-backdrop'>thank you</span>. this is absolutely nuts.
if you don't mind sharing, please tell me about your use case. i'd really love to hear how you use cobalt and how i could make it even more useful for you.