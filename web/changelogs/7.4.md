---
title: "new domain, what's coming in future, bug fixes, and more!"
date: "September 9, 2023"
banner:
    file: "newdomain.webp"
    alt: "text: new domain, same cobalt"
---
cobalt is finally moving to its own domain! many of you have been anticipating this, and many kept forgetting the link due to how cryptic it was.

well, worry no more - <span class="text-backdrop">cobalt.tools</span> is here.

if you haven't yet, open [co.wukko.me](https://co.wukko.me) to transfer your settings here! no additional action from you is required. just open the old link and cobalt will do everything for you :)

make sure to <span class="text-backdrop">update your bookmarks</span> and reinstall the web app!

here's what domain change means:
- still no ads, same owner, same features, same reliability. just a way more rememberable link (it's literally two words).
- cobalt.tools makes it clear that cobalt is a tool and that it's "cobalt", not "wukko".
- i can host various versions of cobalt on subdomains without links looking awkward.
- i can host cobalt-related websites without polluting my personal domain's dns (such as crowdin).
- i stand by same privacy policies (and in fact am using the same exact server as before).

the domain change is required for the future of cobalt.

here's what's coming soon:
- support for many top-requested sites, such as (but not limited to) twitch and niconico.
- education version of cobalt, as often requested by students and educators.
- major localization system upgrade, allowing for simpler community contributions.
- region-specific versions with 100% translations and tweaks.
- native clients for desktop and mobile (not sure about this one, i'm no superman).
- ...and more!

now, here's what's new in 7.4:
- tabs in popups now scroll to top on tab bar tap.
- padding across web app was tuned.
- (obviously) a migration agent. soon will be used for importing and exporting settings.
- some minor clean ups in codebase.

if you want to help cobalt achieve goals listed above, consider donating! donations are the only way i can keep cobalt ad-less, powerful, (basically) limitless, and also 100% free.

in fact, donations have helped me grow cobalt more than i've ever anticipated. just imagine how much better it will be in a year.

go to donations down below to find ways to donate!

thank you for reading through all of this. i hope you enjoy this update and have a great day :D