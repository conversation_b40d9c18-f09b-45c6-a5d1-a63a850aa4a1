---
title: tiktok images and better localization
date: "Sep 3, 2022"
---

- added ability to save images from tiktok conveniently, and without watermarks.
- it's now way easier to contribute translations to cobalt. read more on how to do it [on github](https://github.com/imputnet/cobalt#how-to-contribute-translations). in short, you don't need to fork the repo anymore, everything is handled through crowdin :D
- updated readme in github repo to make it easier to read and understand.
- began to add more descriptive errors, more to come soon.

internal stuff:
- remade entirety of tiktok module and merged it with douyin one. now both (basically identical) platforms have perfect parity of download features.
- cleaned up the twitter module, now it's way more compact and easy to read.
- moved changelog out of english localization.
- other small improvements and fixes.