---
title: "twitter improvements & changelog overhaul"
date: "Nov 15, 2022"
---
- you can download explicit content from twitter.
- direct video links from twitter are properly supported (video/1, video/2, etc.).
- changelog history got support for banners.
- changelog categories are not messy anymore.
- cobalt version in changelogs is now highlighted.
- changelog history got separators to make text easier to read.
- changelog history can be collapsed after loading.
- download button takes less time to change back to pressable state.

if you're a developer and would like to play around with cobalt's api, then read more about it in older changelogs below!