---
title: "mute videos and proper soundcloud support"
date: "Dec 17, 2022"
banner:
    file: "shutup.webp"
    alt: "a cat yawning, with a crossed out loudspeaker icon next to it"
---
i've been longing to implement both of these things, and here they finally are.

service-related improvements:
- you now can download videos with no audio! simply enable the "mute audio" option in settings &gt; audio.
- soundcloud module has been updated, and downloads should no longer break after some time.
visual improvements:
- moved some things around in settings popup, and added separators where separation is needed.
- updated some texts in english and russian.
- version and commit hash have been joined together, now they're a single unit.
internal improvements:
- updated api documentation to include isAudioMuted.
- simplified the startup message.
- created render elements for separator and explanation due to high duplication of them in the page.
- fully deprecated GET method for API requests.
- fixed some code quirks.
here's how soundcloud downloads got fixed:

previously, client_id was (stupidly) hardcoded. that means cobalt wasn't able to fetch song data if soundcloud web app got updated.
now, cobalt tries to find the up-to-date client_id, caches it in memory, and checks if web app version has changed to update the id accordingly. you can see this change for yourself on github.