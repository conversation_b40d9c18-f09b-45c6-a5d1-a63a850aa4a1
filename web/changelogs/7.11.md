---
title: "cache encryption, meowbalt, dailymotion, bilibili, and much more!"
date: "March 6, 2024"
banner:
    file: "meowth7eleven.webp"
    alt: "meowth plush in front of 7-eleven store"
---
cobalt may not have as many groceries as 7-eleven, but it sure does have lots of big changes in this update!

- all cached stream info is now encrypted and can only be decrypted with a link you get from cobalt.
- new popup style featuring me<PERSON><PERSON>, cobalt's speedy mascot. you will see him more often from now on!
- added support for dailymotion (including short links).
- added support for bilibili.tv, fixed support for bilibili.com, and added support for all related short links.
- added support for unlisted vimeo links.
- added support for tumblr audio and revamped the entire module.
- added support for embed ok.ru links.

we also updated the privacy policy to reflect the addition of data encryption, go check it out.

for people with iphones:
- clearer ios saving tutorial.
- added "save to files" ios shortcut.
- updated save to photos shortcut.

make sure to save both shortcuts and read the updated tutorial!

for people who host a cobalt instance:
- updated all environment variables TO_BE_LIKE_THIS. time to update your configs! for now cobalt is backwards compatible with old variable names, but it won't last forever.
- added a list of all environment variables and their descriptions to [run-an-instance doc](https://github.com/imputnet/cobalt/blob/main/docs/run-an-instance.md#list-of-all-environment-variables).
- updated [cookie file example](https://github.com/imputnet/cobalt/blob/main/docs/examples/cookies.example.json) with more services and improved examples.
- updated [docker compose example](https://github.com/imputnet/cobalt/blob/main/docs/examples/docker-compose.example.yml) with better explanations and up-to-date env variable samples.
- updated some packages to get rid of all unnecessary messages in console.

want to host an instance? [learn how to do it here](https://github.com/imputnet/cobalt/blob/main/docs/run-an-instance.md).

frontend changes:
- removed migration popup.
- corners across ui are even more round now.
- bottom glass bkg in popups is no longer rounded on top right.
- small popup no longer stretches like gum, it's fixed in size on desktop.
- small popup animation no longer lags on mobile.
- better ui scaling across resolutions.
- updated donation text.

thank you for using cobalt, all 750k of you. hope you like this update as much as we enjoyed making it :D