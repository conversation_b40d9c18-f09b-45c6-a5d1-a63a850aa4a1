---
title: "the evil has been defeated"
date: "Feb 26, 2023"
banner:
    file: "happymeowth.webp"
    alt: "meowth jumping up into the sky very excitedly"
---
hey, ever wanted to download a youtube video without a hassle? cobalt is here to help. this update fixes all issues related to youtube downloads.
not only that, but it also introduces features never before seen in a downloader, such as youtube dub downloads! read below to see what's up :)

<span class="text-backdrop">tl;dr:</span>

- audio in youtube videos FINALLY no longer gets cut off.
- you now can pick any video resolution you want (from 360p to 8k) and any possible youtube video codec (h264/av1/vp9).
- you now can download youtube videos with dubs in your native language. just check settings > audio.
- youtube processing has been vastly sped up.

ok, now onto the nerdy part of changelog. this update is pretty huge and includes improvements across the board.

service improvements:
- all youtube functionality has been reworked. cobalt now relies on innertube apis, not web scraping.
- random audio cut off issue has been fixed, let me know if it ever occurs again. (closes #62, #66, #75, #88).
- added support for youtube dubs. currently it's using your browser's default language when enabled, but i have plans on making a picker. i'll ask people on twitter and mastodon if this feature is needed, and add a picker in next updates.
- instead of adding more quality presets, i added granular quality options. pick whatever you like, from 360p up to 4320p (for all services, not just youtube).
- replaced a format picker with codec picker for youtube. you can pick h264, av1, or vp9. all of them should work as expected (closes #88).
- youtube audio files are now properly matched to corresponding video files.
- it's now always possible to download pristine h264 720p/360p videos from youtube. these videos will work ANYWHERE, so they're default for mobile.
- youtube requests are no longer permanently cached, ram usage should drop even further.
- youtube video and audio file names now include codec and dub language when applicable.
- max video and audio duration limits have been bumped up to 3 hours.
- general performance of entire youtube download process has been greatly improved.
- vk module has been reworked to be more compact and not make use of outdated technique of quality picking. should also be way more reliable.

internal improvements:
- cleaned up services config, all constants have been moved directly to modules for quicker access.
- matching module has been slightly cleaned up.

interface improvements:
- many descriptions and error messages have been slightly tuned to be less wordy.
- unnecessary title duplications in settings have been merged into one.
- added more clarity to quality and codec descriptions.

if you use cobalt api, please note that you have to update your creation to support new features.

this is the second batch of 5.x improvements, there's way more to come. thank you for being here, i really appreciate your support.

if you want to thank me (the developer), there's a nice tab under this changelog that has "donations" text on it. anything helps me continue developing and hosting the friendliest media downloader :D