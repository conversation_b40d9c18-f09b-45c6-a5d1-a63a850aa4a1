---
title: "everything what you've been waiting for. welcome to cobalt 3.0 :)"
date: "Aug 12, 2022"
---

follow cobalt's twitter account for polls, updates, and more: [@justusecobalt](https://twitter.com/justusecobalt)

stuff that you can notice:

- you can now download audio from any supported service, in any format that you set in settings (+). yes, that includes mp3, which you all have been waiting for :D
- it's now easier to switch between download modes (just a single toggle on the bottom).
- your youtube download format has been reset, sorry, but that was required to implement all audio downloads.
- default download format for youtube videos on all platforms is now webm. except for ios.

- cobalt now has emoji, just to spice up the black and white ui. all of them have been tuned to look the best in both themes. isn't it cool?
- about, changelog, and donation popups have been merged into just one, for covnenience.
- changelog got a huge upgrade (as you can see), and now there are both major changes and latest commit info, just so commits can finally go back to being batshit insane.
- changelog popup appears on every major update, but you can disable it in settings, if you want to.
- changelog now opens by default when pressing "?" button. i don't think anyone reads "about" as often.
- settings (+) have been split into three tabs, also for convenience and ease of use.

- added support for donation links. you can now donate through boosty, not only via crypto :D
- donate popup has been rearranged and tuned just a tiny bit.

- you can now click away from any popup by pressing the void behind it.
- you can also press "escape" key on keyboard to close any popup.

- switchers and buttons are now way easier on eye. white border is gone from where it's unneeded.
- buttons are now very satisfying to press.
- switchers are scrollable if there's not enough space to fit all contents on screen.
- scaling is now even better than before.

internal stuff:

- frontend won't send video related stuff if audio mode is on.
- matching has, yet again, gone through mitosis, and is now probably the cleanest it can get.
- page rendering is now modular, something like what frameworks have but way lighter. this makes adding new features WAY easier.
- removed some stuff that didn't make sense (like storing language of stream request).
- cleaned up insides of cobalt, of course.
- almost all links now open in new tab, just like they should have from the very beginning.

known issues:
- impossible to download audio from vk. i'll try to fix it in the next update.
- headers are not sticky in tabbed popups. maybe this is a good thing, i'll think about it.

if you ever notice any issues, make sure to report them on github. your report doesn't have to sound professional, just do your best to describe the issue.