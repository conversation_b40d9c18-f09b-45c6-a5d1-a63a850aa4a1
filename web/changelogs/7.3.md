---
title: "extended video length limit, metadata toggle, ui improvements, and more!"
date: "September 6, 2023"
banner:
    file: "meowthsnap.webp"
    alt: "cartoon meowth pointing paw dramatically and saying something"
---
this update gives cobalt a sharp look in chromium browsers and makes it even more useful than before. check out the full changelog below!

service improvements:
- increased video length limit from 3 hours to 5 hours. feel free to download lectures you need :)
- you can now disable file metadata in settings.
- fixed a bug which previously caused some downloads to end up being 0 bytes.

ui improvements:
- fixed clickable area for urgent notice (text on top).
- fixed blurry header in chrome.
- fixed blurry tab bar in chrome.
- fixed blurry switches in chrome.
- fixed weirdly rounded corners in popups.
- fixed 1px gap on edges of various elements in popup in chrome.
- fixed overscrolling in other settings tab on ios.
- fixed unexpected button highlight effect on phones.
- removed outdated fixes for tiny screens.

other improvements:
- cobalt web & api start faster than before, additional preparation functions aren't unexpectedly run anymore.
- cobalt is now available as a docker package. check it out on [github](https://github.com/imputnet/cobalt/pkgs/container/cobalt).

thank you for being here. i hope you have a great day :D