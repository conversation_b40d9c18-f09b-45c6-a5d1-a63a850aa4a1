---
title: "support for multi media tweets is here!"
date: "Oct 9, 2022"
---
cobalt now lets you save any of the videos or gifs in a tweet. even if there are many of them.

simply paste a link like you'd usually do and cobalt will ask what exactly you want to save.

FIREFOX USERS: if you have strict tracking protection on, you might wanna turn it off for cobalt, or else twitter video previews won't load. firefox filters out twitter image cdn as if it was a tracker, which it's not. it's a false-positive.

however, you can leave it on if you're fine with blank squares and video numbers. i have thought of that in prior, you're welcome.

other changes:
- repurposed ex tiktok-only image picker to be dynamic and adapt depending on content to pick. that's exactly how twitter multi media downloads work.
- cobalt is now properly viewable on phones with tiny screens, such as first gen iphone se.
- scrollbars now should be visible only where they're needed.
- brought back proper twitter api, because other one doesn't have multi media stuff (at least yet).
- cleaned up some internal files, including main frontend js file.
- reorganized some files in project directory, now you won't get lost when contributing or just looking through cobalt's code.