---
title: "support for twitch clips and rutube!"
date: "September 16, 2023"
banner:
    file: "twitchupdate.webp"
    alt: "meowth plush staring into the camera, laptop with generic purple service in the background"
---
hey! this update (finally) adds support for twitch clips and rutube, among other smaller changes.

service improvements:
- added support for twitch clips. no vods, they're unnecessary. just clip whatever you want to download!
- added support for rutube in case you ever wanted to download something russian.

interface improvements:
- added a note about cobalt not being affiliated with any supported services.
- added a note about meta (the company) in russian.
- better russian localization. will keep improving it to make it sound not so robotic over time.

other improvements:
- all official servers are now using the docker package. and so should you!
- moved the load balancer to poland. requests should be slightly faster now.
- minor codebase clean up.

if you're confused about the new domain, read the older changelog! just scroll lower and press "expand".

i hope you find this update useful and have a wonderful day :)

btw, cobalt has a pretty active community server on discord. go to about > support & source code to join!