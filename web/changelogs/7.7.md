---
title: "bugfixes and better downloads!"
date: "December 2, 2023"
banner:
    file: "meowthpolishegg.webp"
    alt: "meowth polishing a togepi egg"
---
this update fixes various issues with supported services. no new features yet, but twitter fix is surely something good to have in the meantime!

service improvements:
- broken twitter videos are now automatically fixed by cobalt.
- all vimeo videos and audios should now be possible to download.
- vimeo: fixed short resolution displayed in "basic" and "pretty" filename styles.

interface improvements:
- streamables are now easier to save on ios.

internal improvements:
- port env variable is now not strictly necessary for cobalt to run.
- minor clean up.

changes since 7.6:
- fix for an issue related to youtube dubs.
- fixed a memory leak related to live renders.
- handling all errors related to twitter downloads.
- fixed support for reddit links in various languages.
- added rich filenames support for twitch clips.
- updated support and donation lists.

stay tuned for future updates and have a great day :D