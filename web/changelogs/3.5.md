---
title: "ui revamp and usability improvements"
date: "Sep 8, 2022"
---
new features:
- cobalt now lets you paste the link in your clipboard and download the file in a single press of a button.if your clipboard's latest content isn't a valid url, cobalt won't process or paste it. you can also hide the clipboard button in settings if you want to.
unfortunately, the clipboard feature is not available to firefox users because mozilla didn't add proper support for clipboard api.
- there's now a button to quickly clean the input area, right next to download button. it's really useful in case when you want to quickly save a bunch of videos and don't want to bother selecting text.
- keyboard shortcuts! you love them, i love them, and now we can use them to perform quick actions in cobalt. use ctrl+v combo to paste the link without focusing the input area; press escape key to close the active popup or clean the input area; and if you didn't know, you can also press enter to download content from the link.

new looks:
- main box has been revamped. it has lost its border, thick padding, and now feels light and fresh.
- download button is now prettier, and has been tuned to make >> look just like the logo.
- buttons on the bottom now actually look like buttons and are way more descriptive. no more #@+?$ bullshit. it's way easier to see and understand what each of them does.
- bottom buttons are prettier and easier to use on a phone. they're bigger and stretch out to sides, making them easier to press.

fixes:
- it's now impossible to overlap multiple popups at once. no more mess if you decide to explore popups while waiting for request to process.
- popup tabs have been slightly moved down to prevent popup content overlapping.
- ui scalability has been improved.