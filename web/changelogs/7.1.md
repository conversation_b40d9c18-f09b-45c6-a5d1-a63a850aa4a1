---
title: "instagram, streamable, video metadata, and more!"
date: "August 20, 2023"
banner:
    file: "meowthproductions.webp"
    alt: "meowth roaring in a fancy circle, à la MGM studios intro"
---
service improvements:
- extended instagram support: high quality photos, videos, reels. everything should work without any issues, enjoy! :)
- added support for streamable.com (thanks to [#179](https://github.com/imputnet/cobalt/pull/179))
- added video metadata to youtube videos.
- fixed vk video downloads.
- vxtwitter links are now supported.
- fixed support for youtube audio dubs.

ui improvements:
- fixed picker popup: it's now scrollable in all cases and clickable areas don't overlap each other.

backend improvements:
- cobalt will now let you know if something goes wrong during video download instead of nuking the stream.
- added support for cookies (thanks to [#177](https://github.com/imputnet/cobalt/pull/177))
- replaced got with undici (thanks to [#182](https://github.com/imputnet/cobalt/pull/182)). downloads should be slightly faster and clean of garbage in headers.

internal improvements:
- moved host overrides into its own module.
- minor clean ups.

even more cool stuff is coming in future updates! thank you for using cobalt :D