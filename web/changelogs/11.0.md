---
title: "local media processing, better performance, and a lot of polish"
date: "29 May, 2025"
banner:
    file: "meowth_beach.webp"
    alt: "meowth plush with obnoxious sunglasses on foreground, very close to the camera. sunset and beach in background."
---

long time no see! it's almost summer, the perfect time to create or discover something new. we've been busy working in the background to make cobalt better than ever, but now we're finally ready to share the new major version.

as a part of the major update, we revised our [terms of use](/about/terms) & [privacy policy](/about/privacy) to reflect new privacy-enhancing features & to improve readability; you can compare what exactly changed in [this commit](https://github.com/imputnet/cobalt/commit/be84f66) on github. **nothing changed about our principles or dedication to privacy**, but we still thought it'd be good to let you know.

here are the highlights of what's new in cobalt 11 and what else has changed since the last changelog in december:

## on-device media processing (beta)
cobalt can now perform all media processing tasks *directly in your browser*. we enabled it by default on all desktop browsers & firefox on android, but if you want to try it on your device before we're sure it works the way we expect, you can do it in a new [local processing page in settings](/settings/local)!

here's what it means for you:
- **best file compatibility**, because all processed files now have proper headers. there's **no need to remux anything manually** anymore; all editing software should support cobalt files right away! vegas pro, logic pro, many DAWs, windows media player, whatsapp, etc, — all of them now support files from cobalt *(but only if they were processed on-device)*.
- **detailed progress** of file processing. cobalt now displays all steps and the current progress of all of them. no more guessing when's the file gonna be ready.
- **faster processing** for all tasks that require remuxing or transcoding, such as downloading youtube videos, transcoding audio, muting videos, or converting gifs from twitter.
- **better reliability** of all processing tasks. cobalt can finally catch all processing errors properly, meaning that the corrupted file rate will drop significantly. if anything ever goes wrong, cobalt will let you know, and you'll be able to retry right away!
- **reduced load on public instances**, which makes cobalt faster than ever for everyone. servers will no longer be busy transcoding someone's 10 hour audio of "beats to vibe and study to" — because now their own device is responsible for this work. it's really cool!

we're also introducing the processing queue, which allows you to schedule many tasks at once! it's always present on the screen, in the top right corner. the button for it displays precise progress across all tasks, so you know when tasks are done at a glance.

all processed videos are temporarily stored on your device and are automatically wiped when you reload the page. no need to rush saving them; they'll be there as long as you don't close cobalt or delete them from the queue.

on modern ios (18.0+), we made it possible to download, process, and export giant files. the limit is now your device's storage, so go wild!

processing queue & local processing may not be perfect as-is, so please let us know about any frustrations you face when using them! this is just the beginning of an on-device era of cobalt. we hope to explore even more cool local processing features in the future.

## web app improvements: ui/ux upgrade and svelte 5
aside from local processing, we put in a ton of effort to make the cobalt web app faster and even more comfortable for everyone. we fixed all minor ui nicks, polished it, and improved turnstile behavior!

- **svelte 5:** many parts of cobalt's frontend have been migrated to svelte 5. this is mostly an internal change, but it majorly improves the performance and reduces extra ui renders, making the overall experience snappier.
- **downloading flow:**
    - cobalt will now start the downloading task right away, even if turnstile is not finished verifying the browser yet. it will wait for turnstile's solution instead of showing an annoying dialog.
    - pressing "paste" before turnstile is finished now starts the download right away.
    - prefilled links via url parameters (`#urlhere` or `?u=urlhere`) are now downloaded right away. one less button press!
    - replaced an invasive turnstile dialog with a dynamic tooltip.
    - ideally, you should no longer know that cloudflare turnstile is even there.
- **remux**:
    - remux is now a part of the processing queue! the remux page now serves as an importer. no need to stay on the same page for remux to complete.
    - you can now remux several files at once.
    - cobalt now automatically filters out unsupported files on import, so you can drag and drop whatever.
- **visuals & animations:**
    - the dialog animation & visual effects have been optimized to improve performance. the picker dialog no longer lags like hell!
    - all images now fade in smoothly on load.
    - the update notification now has a new, springier animation.
    - enhanced focus rings across the whole app for better accessibility, a cleaner look, and ease of internal maintenance.
    - sidebar is now bright in light theme mode on desktop, and is more visible in dark mode.
    - sidebar buttons are now more compact.
    - the status bar color on desktop (primarily safari) now adapts to the current theme.
    - fixed many various rendering quirks in webkit and blink.
    - all input bars are now pressable everywhere.
    - popovers (such as supported services & queue) are now rendered only when needed.
    - the font on the about/changelog pages is now consistent with the rest of the ui (IBM Plex Mono).
    - all image assets have been re-compressed for even faster loading.
    - the download button now uses a super tiny custom font instead of a full noto sans mono font.
    - countless padding, margin, and alignment tweaks for overall consistency and a fresh vibe.
- **accessibility & usability:**
    - created a dedicated [accessibility settings page](/settings/accessibility) and moved relevant settings there.
    - improved screen reader accessibility & tab navigation across ui.
    - added an option to prevent the processing queue from opening automatically in [accessibility settings](/settings/accessibility#behavior).
    - files now save properly on desktop in pwa mode (when using local processing).
- **ios-specific improvements**:
    - added haptic feedback to toggles, switchers, buttons, dropdowns, and error dialogs. not a fan of haptics? disable them in [accessibility settings](/settings/accessibility/haptics).
    - made it possible to process giant files without crashing on ios 18.0+. the cobalt tab/pwa no longer crashes if a file is too big for safari to handle. *(previously anything >384mb lol)*
    - improved file saving, now cobalt selects the most comfortable way to save a file automatically.
- **settings page:**
    - sensitive inputs (like api keys) are now hidden by default with an option to reveal them.
    - added an option to hide the remux tab on mobile devices in [appearance settings](/settings/appearance#navigation).
    - filename previews in settings now more accurately reflect the actual output.
    - improved the toggle animation.
    - redesigned settings page icons.
    - updated some descriptions to be more accurate.
- all [about](/about) pages have been revised for improved readability and clarity.
- the web instance now requires the `WEB_DEFAULT_API` env variable to run. it's enforced to avoid any confusion.
- the plausible script is no longer loaded when anonymous analytics are disabled.

## general improvements
- filenames can now include a wider range of characters, thanks to relaxed sanitization & use of fullwidth replacements.
- "basic" is now the default filename style.

## processing instance improvements
- env variables can now be loaded & updated dynamically. this allows for configuration changes without downtime!
- tunnels now provide an `Estimated-Content-Length` header when exact file size isn't available.
- many internal tunnel improvements.
- the api now returns a `429` http status code when rate limits are hit.
- the `allowH265` (formerly `tiktokH265`) and `convertGif` (formerly `twitterGif`) api parameters have been renamed for clarity as they apply (or will apply) to more services.
- added a bunch of new [api instance environment variables](https://github.com/imputnet/cobalt/blob/main/docs/api-env-variables.md): `FORCE_LOCAL_PROCESSING`, `API_ENV_FILE`, `SESSION_RATELIMIT_WINDOW`, `SESSION_RATELIMIT_MAX`, `TUNNEL_RATELIMIT_WINDOW`, `TUNNEL_RATELIMIT_MAX`, `CUSTOM_INNERTUBE_CLIENT`, `YOUTUBE_SESSION_SERVER`, `YOUTUBE_SESSION_INNERTUBE_CLIENT`, `YOUTUBE_ALLOW_BETTER_AUDIO`.

## youtube improvements
- added a new option in [audio settings](/settings/audio#youtube-better-audio) to prefer better audio quality from youtube when available.
- near infinite amount of changes and improvements on cobalt & infrastructure levels to improve reliability and recover youtube functionality.
- cobalt now returns a more appropriate error message if a youtube video is locked behind drm.
- added itunnel transplating to allow in-place tunnel resume after an [intentional] error from origin's side.

## other service improvements
- added support for **xiaohongshu**.
- **twitter:**
    - added support for saving media from ad cards.
    - added fallback to the syndication api for better reliability due to constant twitter downtimes & lockdowns.
- **reddit:**
    - expanded support for various link types, including mobile (e.g., `m.reddit.com`) and many other short link formats.
- **instagram:**
    - added support for more links, including the new `share` format.
    - implemented more specific errors for age-restricted and private content.
    - fixed an issue where posts might have not correctly fallen back to a photo if a video URL was missing.
- **tiktok:**
    - added support for tiktok lite urls.
    - fixed parsing of some mobile tiktok links.
    - updated the primary tiktok domain used by the api due to previous dns issues.
- **snapchat:**
    - fixed an issue where story extraction could fail if certain profile parameters were missing.
    - added support for new link patterns.
- **pinterest:**
    - fixed video parsing for certain types of pins.
- **bluesky:**
    - added support for downloading tenor gifs from bluesky posts.
- **odnoklassniki (ok):**
    - fixed an issue where author information wasn't handled properly.
- **loom:**
    - added support for links with video titles.
    - fixed support for more video types.
- **facebook:**
    - fixed issues caused by rate limiting.

## documentation improvements
- created a new document for [api instance environment variables](https://github.com/imputnet/cobalt/blob/main/docs/api-env-variables.md) with detailed & up-to-date info about each variable.
- rewrote [api docs](https://github.com/imputnet/cobalt/blob/main/docs/api.md) to be easier to read and added all new & previously missing info.
- updated the list of dependencies & open-source shoutouts in [api](https://github.com/imputnet/cobalt/blob/main/api/README.md) & [web](https://github.com/imputnet/cobalt/blob/main/web/README.md) readme docs.
- added an example for setting up `yt-session-generator` in the docker compose documentation.
- updated the "run an instance" guide with a more prominent note about abuse prevention.

## more internal improvements
- introduced an abstract storage class and implemented opfs (origin private file system) and memory storage backends. this is the foundation of the new local processing features, and makes it possible to operate on devices with low RAM.
- session tokens are now bound to ip hashes, locking down their usage and improving security.
- lots of other refactoring and code cleanups across both api and web components.
- numerous test fixes, additions, and ci pipeline improvements.
- removed unused packages & updated many dependencies.

## all changes are on github
like always, you can check [all commits since the 10.5 release on github](https://github.com/imputnet/cobalt/compare/41430ff...a52dde7) for even more details, if you're curious.

this update was made with a lot of love and care, so we hope you enjoy it as much as we enjoyed making it.

that's all for now, we wish you an amazing summer!

\~ your friends at imput ❤️