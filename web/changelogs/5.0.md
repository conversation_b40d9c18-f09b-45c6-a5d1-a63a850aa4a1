---
title: "it's all about attention to detail!"
date: "Feb 13, 2023"
banner:
    file: "valentines.webp"
    alt: "relaxed meowth with sakura petals falling in front of them"
---
happy valentine's day! i have an update for you, as a gift :D

tl;dr: added support for <span class="text-backdrop">reddit gifs</span>, fixed douyin downloads, fixed vimeo quality picking, revamped entirety of codebase, and many other fixes.

here's more info:

this update is mostly about cleaning up and polishing the codebase, but it also has some new features. here's what's up:

service-related improvements:
- you now can download gifs from reddit!
- attempting to download a video from douyin no longer throws an error (bytedance changed the api endpoint, yet again).
- fixed quality picking for vimeo downloads.
- fixed length limit check in vimeo module.
- fixed support for "user view" vk clips links.
- various twitter errors are now displayed correctly instead of falling back to the default error.
- state of all services is now tested on each commit.

ui improvements:
- cobalt social links no longer disappear if you have an aggressive ad blocking extension installed.
- various localization improvements for both english and russian.
- changed some service aliases to display full list of supported downloads.
- added current branch information to version text (in settings).
- fixed typos in older changelogs.

internal improvements:
- <span class="text-backdrop">everything</span> has been sanitized, improved, and refactored. code is now much easier to read and maintain.
- rewrote and/or optimized all modules that were messy or inefficient.
- all git interaction functions now store info in cache instead of fetching it every time the function is called.
- added a test script that checks functionality of all supported services.
- updated deepsource config. checks are more accurate now.
- requests from internet explorer are now dropped entirely instead of redirecting people stuck in 90s to a proper browser download page. this was done to avoid (my) personal bias towards browsers.

i put a ton of effort into this version, and i hope you like it as much as i do.

thank you for using cobalt. there's so much more to come :)