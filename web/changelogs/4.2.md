---
title: "optimized quality picking and 8k video support"
date: "Nov 4, 2022"
---
- this update fixes quality picking that was accidentally broken in 4.0 update.
- you now can download videos in 8k from youtube. why would you that? no idea. but i'm more than happy to give you this option.
- default video quality for downloads from pc is now 1440p, and 720p for phones.
- default video format is now mp4 for everyone.
- default audio format is now mp3 for everyone.

you can always change new defaults back to whatever you prefer in settings.

other changes:
- added more clarity to quality picker description.
- youtube video codecs are now right in the picker.
- setup script is now easier to understand.