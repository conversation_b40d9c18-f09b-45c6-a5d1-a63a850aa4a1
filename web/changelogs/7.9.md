---
title: "twitter gifs, pinterest, ok.ru, and more!"
date: "January 17, 2024"
banner:
    file: "meowthball.webp"
    alt: "meowth rolling on a big catnip ball"
---
yes, you read that right. cobalt now lets you convert any twitter gif to an actual .gif file! (finally)
just go to settings and enable this feature :)

service improvements:
- added an option to [convert gifs from twitter](https://github.com/imputnet/cobalt/issues/250) into actual .gif format. files will be bigger and lower quality, but maybe you want that.
- pinterest support has been completely redone, now all videos ([and even pin.it links](https://github.com/imputnet/cobalt/issues/160)) are supported.
- added [support for ok.ru](https://github.com/imputnet/cobalt/issues/322) in case you're a russian grandma.
- now processing [all reddit links](https://github.com/imputnet/cobalt/issues/318) (including old.reddit.com).
- [instagram live vods](https://github.com/imputnet/cobalt/issues/316) are now supported.
- fixed a [rare vimeo bug](https://github.com/imputnet/cobalt/issues/289) related to 1440p videos.

other improvements:
- ui fade in animation is no longer present if you've disabled animations.
- all images now have alt descriptions.
- cobalt html is now [biblically correct](https://github.com/imputnet/cobalt/issues/317) and follows the html spec.
- lots of cleaning up.

patches since 7.8:
- shift+key [shortcuts are now ignored](https://github.com/imputnet/cobalt/issues/288) if url bar is focused.
- longer soundcloud links are now supported, also catching more tiktok-related errors.
- removed mastodon from support links as that account is no longer active.
- added ability to download a specific video from multi media tweets and support for /mediaViewer links.
- fixed [modal blurriness](https://github.com/imputnet/cobalt/issues/309) in chromium.
- minor html changes (road to biblically correct one).

lots of long-awaited updates (especially twitter gifs), hope you enjoy them and have a great day :D