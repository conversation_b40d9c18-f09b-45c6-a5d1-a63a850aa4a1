---
title: "improvements all around!"
date: "Sep 28, 2022"
---
- download mode switcher is moving places, it's now right next to link input area.
- smart mode has been renamed to auto mode, because this name is easier to understand.
- all spacings in ui have been evened out. no more eye strain.
- added support for twitter /video/1 links
- clipboard button exception has been redone to prepare for adoption of readtext clipboard api in firefox.
- cobalt is now using different tiktok api endpoint, because previous one got killed, just like the one before.
- "other" settings tab has been cleaned up.