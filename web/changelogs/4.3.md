---
title: "developers, developers, developers, developers"
date: "Nov 12, 2022"
banner:
    file: "developers.webp"
    alt: "steve ballmer going \"developers, developers, developers\""
---
this update features a TON of improvements.

[developers](https://www.youtube.com/watch?v=SaVTHG-Ev4k), you now can rely on cobalt for getting content from social media. the api has been revamped and [documentation](https://github.com/imputnet/cobalt/tree/main/docs/api.md) is now available. you can read more about API changes down below. go crazy, and have fun :D

if you're not a developer, here's a list of changes that you probably care about:
- rate limit is now approximately 8 times bigger. no more waiting, even if you want to download entirety of your tiktok "for you" page.
- some updates will now have expressive banners, just like this one.
- fixed what was causing an error when a youtube video had no description.
- mp4 format button text should now be displayed properly, no matter if you touched the switcher or not.

next, the star of this update — improved api!
- main endpoint now uses POST method instead of GET.
- internal variables for preferences have been updated to be consistent and easier to understand.
- ip address is now hashed right upon request, not somewhere deep inside the code.
- global stream salt variable is no longer unnecessarily passed over a billion functions.
- url and picker keys are now separate in the json response.
- cobalt web app now correctly processes responses with "success" status.

if you currently have a siri shortcut or some other script that uses the GET method, make sure to update it soon. this method is deprecated, limited, and will be removed entirely in coming updates.

if you ever make something using cobalt's api, make sure to mention [@justusecobalt](https://twitter.com/justusecobalt) on twitter, i would absolutely love to see what you made.