---
title: "better and faster than ever"
date: "Oct 24, 2022"
---
this update has a ton of improvements and new features.

changes you probably care about:
- cobalt now has support for recorded twitter spaces! download the previous conversation no matter how long it was.
- download speeds from youtube are at least 10 times better now. you're welcome.
- both video and audio length limits have been extended to 2 hours.
- audio downloads from youtube, youtube music, twitter spaces, and soundcloud now have metadata! most often it's just title and artist, but when cobalt is able to get more info, it adds that metadata too.
- tiktok downloads have been fixed, yet again, and if they ever break in the future, cobalt will fall back to downloading a less annoyingly watermarked video.
- soundcloud downloads have been fixed, too.

less notable changes:
- currently experimenting with using mp3 as default audio format. if you set something other than mp3 before, it'll be set to mp3. you can always change it back in settings. let me know what you think about this.
- "download audio" button from image picker no longer stays on the screen after popup was closed.
- clipboard button now shows up depending on your browser's support for it.
- you can no longer manually hide the clipboard button, 'cause it's unnecessary.
- small internal improvements such as separation of changelog version and title.
- fair bit of internal clean up.

if you want to help me implement covers for downloaded audios, [you can do it on github](https://github.com/imputnet/cobalt).