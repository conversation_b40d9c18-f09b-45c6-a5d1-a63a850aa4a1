---
title: "customizable file names, instagram stories, and first cobalt sponsor!"
date: "October 15, 2023"
banner:
    file: "meowthcenter.webp"
    alt: "meowth plush in a datacenter wearing a hardhat, wielding a hammer"
---
as many have (very) often requested, cobalt now lets you pick between several file name format styles!
go to <span class="text-backdrop">settings > other</span> and change it to whichever you like! there's a preview of each style, so you know how exactly files are gonna look like.

if you liked file names the way they were before, don't worry: classic style is still the default :)

on a different but not any less important note: cobalt is now sponsored by [royalehosting.net](https://royalehosting.net/)!
overall service performance and stability is gonna be better, but also more content will be possible to download thanks to geniuine server locations. and yes, still no ads or trackers.

this update also includes a bunch of other changes, check them out:

service improvements:
- added support for instagram stories thanks to [#194](https://github.com/imputnet/cobalt/pull/194).
- fixed reddit support thanks to [#221](https://github.com/imputnet/cobalt/pull/221).
- added support for rich file names for youtube, vimeo, soundcloud, rutube, and vk.
- numbers and emoji no longer disappear from file name and metadata.
- mute and audio dub file name tags don't appear together anymore.
- youtube: dub file name tag doesn't appear anymore if audio track is default.

interface improvements:
- added a list of sponsors to about tab. if you host an instance, it's disabled by default, but can be enabled with showSponsors env variable.
- about button now opens about tab when no new changelog is available.
- fixed download button thickness on ios.

you now can reach out to cobalt via email for support! it's located in the about tab along with other socials, such as discord.

i hope you enjoy this long-awaited update and have a blissful day :D