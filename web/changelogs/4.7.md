---
title: "we're better together! thank you for bug reports."
date: "Jan 13, 2023"
banner:
    file: "bettertogether.webp"
    alt: "various different pokémon jumping in happiness"
---
this update includes a bunch of improvements, many of which were made thanks to the community :D

service-related improvements:
- private soundcloud links are now supported (#68);
- tiktok usernames with dots in them no longer confuse cobalt (#71);
- .ogg files no longer wrongfully include a video channel (#67);
- fixed an issue that caused cobalt to freak out when user attempted to download an audio from audio-only service with "mute video" option enabled.

ui improvements:
- popup padding has been evened out. popups are now able to fit in more information on scroll, especially on mobile;
- all buttons are now of even size and are displayed without any padding issues across all modern browsers and devices;
- checkbox is no longer crippled on ios;
- many explanation texts have been simplified to get rid of unnecessary bloat (no bullshit, remember?);
- moved tiktok section in video settings higher due to higher priority;
- fixed unexpectedly displayed scrollbars on switch rows in firefox.

stability improvements:
- ffmpeg process now should end upon finishing the render;
- ffmpeg should also quit when download is abruptly cut off;
- fixed a memory leak that was caused by misconfigured stream information caching (#63).

internal improvements:
- requested streams are now stored in cache for 2 minutes instead of 1000 hours (yes, 1000 hours, i fucked up);
- cached data is now reused if user requests same content within 2 minutes;
- page render module is now even cleaner than before;
- proper support for bullet-points in loc strings.

you can suggest features or report bugs on [github](https://github.com/imputnet/cobalt) or [twitter](https://twitter.com/justusecobalt). both work just fine, use whichever you're more comfortable with.

thank you for using cobalt, and thank you for reading this changelog.

you're amazing, keep it up :)