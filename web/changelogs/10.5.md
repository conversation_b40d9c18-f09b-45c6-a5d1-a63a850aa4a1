---
title: "merry christmas and happy new year!"
date: "23 Dec, 2024"
banner:
    file: "newyear2025.webp"
    alt: "meowth plush in a christmas hat sitting in front of a shiny christmas tree."
---

## where the elves at?
we are back once again with another cobalt update, whether you like it or not! just like santa, we come when you least expect us.

we're back to the battlefield against youtube's scraper flattener, but we're winning so far! we even managed to squeeze in a ton of improvements that range from performance bumps to ui overhauls to brand new features. make sure to read further or you might end up on the naughty list...

## even more youtube improvements
- countless infrastructure improvements and developments that allowed us to keep youtube support available during the worst times.
- improved youtube codec fallback. now cobalt goes through all codecs to find you the best one!
- improved youtube video quality selection & fallback.

## improvements for other services
- added support for loom's video embed links.
- added support for facebook's mobile subdomain links.
- fixed a bug in the instagram module where it wouldn't use the graphql api on failure, due to which cobalt was unable to load slightly more posts successfully. now the majority of posts are accessible!
- removed support for vine because &#120143;, "The Everything App", broke the vine archive.
- increased performance of downloads from bluesky by using the video cdn directly.
- error messages from bluesky module are now more descriptive.
- rewrote the vk video extraction module to use the general api as the web app extraction was broken by a vk update.
- added support for new vk video links.
- cobalt now shows an appropriate error if:
    - soundcloud track is region locked or paywalled.
    - tiktok post is age restricted or otherwise unavailable.
    - rutube video is region locked.
    - vk video is region locked.

## web app (and ui/ux) improvements
- added support for [instance access keys](/settings/instances#access-key)! now you can access private cobalt instances with no turnstile, directly from the web app.
- redesigned the [remux page](/remux) to indicate better what remuxing does and what it's for.
- majorly improved the reliability of turnstile. it no longer gets stuck in the background, and cobalt always keeps track of its state and displays it in the omnibox.
- rewrote almost all error messages in an effort to make them easier to understand at a glance.
- added more error messages to describe processing issues even better whenever possible.
- added animations to omnibox icons that make them more lively and cute.
- improved the toggle animation, made it stretchy and jumpy just like the rest of the ui.
- made the cobalt web app fully compatible with RTL languages (such as arabic).
- added an automatically generated sitemap, making the web app easier to index by search engine crawlers.
- made it way easier to override the selfhosted processing instance in a selfhosted web app.
- removed an extra security warning in the selfhosted web app which appeared when the processing instance didn't match the default one.
- added the "community instance" label to the web app that appears on instances different from the official one, making it easier to differentiate them from one another.
- updated cobalt embed description to be less corny.
- fixed a bug that caused settings to be exported improperly on ios in PWA mode. now they're extracted via the share api, just like all other files!
- fixed the weird focus borders in chromium browsers that appeared after a recent browser update.
- optimized rendering of the _supported services_ popover & updated its animation.
- improved accessibility of the web app all around.
- other tiny but mighty changes.

*~ 🦆🔜 ~*

## processing instance improvements
- added support for one more way of youtube authentication: web cookies with poToken & visitorData, so that everyone can access youtube on their instances again!
- significantly refactored the cookie system for better error resistance.
- added success and error console messages to indicate whether cookies/keys were loaded successfully.
- cobalt now warns if it was unable to save updated cookies back to the file.
- majorly refactored the youtube module and removed unnecessary extra loops.
- cobalt no longer loads unnecessary data from youtube when not needed.
- fixed a bug where cobalt tried to proxy URLs on local network when global proxy was configured.
- fixed a bug that caused some HLS videos to be impossible to download in the "mute" download mode.
- fixed a bug where cobalt stacked HLS streams several times within itself which caused heavily reduced performance.
- fixed a bug where cobalt did not use a dispatcher on a HLS stream's chunks, sometimes causing it to access content from an incorrect IP address.
- refactored automatic testing CI, made service tests easier to manage.
- reduced docker container privileges to a regular user.
- improved rich filename & metadata support. all metadata is now added to the file "as-is" with no modifications at all. filenames are now compatible with all operating systems and files should never appear as "tunnel", even in some rare cases.

## more details
as always, you can check [all commits since the last release on github](https://github.com/imputnet/cobalt/compare/c021293...41430ff) for *literally all changes!*

## thank you!
our [github repo](https://github.com/imputnet/cobalt) reached over 20k stars recently, and around the same time the cobalt web app reached over 150k daily visitors. both of these numbers are insane to think about, thank you so much for your support!

this is the last big update of 2024, the most transformative and exciting year for cobalt yet.
we're already working on new cool features that'll come out next year :3

we hope you have amazing holidays and 2025!

\~ your jolly friends at imput 🎄

## donate to imput
plz [donate](/donate), we as elves work all day and night

![sad hampter in a christmas hat](data:image/webp;base64,UklGRn4BAABXRUJQVlA4IHIBAAAwDgCdASpAAEkAP1Waw1oxqqckKbqq2jAqiWIA0kkRgW9ViVdiWdXKQi6/gdi6yh7EP2hdKybn20T+5U2HORdT1INF/azUgAe83P37UIt7DaMNjNpN1q36xYwYmvqRvyHZCbmjuEi8jMI5QwpK+A6PL5WzAeMK1HHSwAD+6mC6qPoWsYNuVCVokfhT4iULSdrgIUxMVYuFmvaB6EO1tiQsDKgGz3TT/evh4KRuHM3hK23nOULaAYPQUKFqt6mmdlUXEnnkybyuQspqBd7vYu7KCfAgexNvxKgitS1o+4JfpkOuihhRfUFRqB2Z63FsbgywZxKR9zkIWWPVYn5XIBJX6LS+AU0fc7hHnV7I0boYFlIgvVJQX0k1Tcuvk4aS9UnxcZXhLIrob7G+vHgUt4z1jVbRN+cMa/ymg+mH2qtsTW1QyhZqaerV930ZZFSsPbSlxCabNU46cRYJ3EIYwxRS6n16lWtc1hKg3Tk23rG9AAAA)
![one more sad hampter in a christmas hat](data:image/webp;base64,UklGRn4BAABXRUJQVlA4IHIBAAAwDgCdASpAAEkAP1Waw1oxqqckKbqq2jAqiWIA0kkRgW9ViVdiWdXKQi6/gdi6yh7EP2hdKybn20T+5U2HORdT1INF/azUgAe83P37UIt7DaMNjNpN1q36xYwYmvqRvyHZCbmjuEi8jMI5QwpK+A6PL5WzAeMK1HHSwAD+6mC6qPoWsYNuVCVokfhT4iULSdrgIUxMVYuFmvaB6EO1tiQsDKgGz3TT/evh4KRuHM3hK23nOULaAYPQUKFqt6mmdlUXEnnkybyuQspqBd7vYu7KCfAgexNvxKgitS1o+4JfpkOuihhRfUFRqB2Z63FsbgywZxKR9zkIWWPVYn5XIBJX6LS+AU0fc7hHnV7I0boYFlIgvVJQX0k1Tcuvk4aS9UnxcZXhLIrob7G+vHgUt4z1jVbRN+cMa/ymg+mH2qtsTW1QyhZqaerV930ZZFSsPbSlxCabNU46cRYJ3EIYwxRS6n16lWtc1hKg3Tk23rG9AAAA)
