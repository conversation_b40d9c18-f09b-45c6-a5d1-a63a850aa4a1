---
title: "better reliability, new infrastructure, pinterest support, and way more!"
date: "June 7, 2023"
banner:
    file: "catswitchboxes.webp"
    alt: "a cat climbing into two empty boxes of asahi beer"
---
hey! long time no see, hopefully over 40 changes will make up for it :)

cobalt now has an official community discord server. you can go there for news, support, or just to chat. [go check it out!](https://discord.gg/pQPt8HBUPu)

<span class='text-backdrop'>tl;dr</span>

- new infra, new hosting structure, new main instance api url. developers, [get it here](https://github.com/imputnet/cobalt/blob/main/docs/api.md).
- added support for pinterest, vine archive, tumblr audio, youtube vr videos.
- better web app performance and look.
- better stability thanks to load balancing.
- (hopefully) no more random video/audio download drops.

service improvements:
- added support for pinterest videos and stories (pr by [@Snazzah](https://github.com/imputnet/cobalt/commit/40291c4d24cb5f441cdddfd26104f149bc4ee27c)).
- added support for tumblr audio. sorry, tumblr.
- added support for youtube vr videos. please note that they're in youtube's proprietary ratio.
- added support for vine archive.
- added support for ancient vk videos in 240p.
- fixed an issue related to muted video downloads from tumblr.
- moved to twitter v2 api.
- soundcloud share links are now processed without errors.

ui improvements:
- lazy image loading. should significantly speed up the page load.
- fixed checkbox width on mobile devices.
- addition of a temporary urgent notice.
- added hover border to all buttons.
- less annoying donation button highlight.
- more consistent color scheme.
- added link to a discord server into about popup.
- remember celebratory emoji changes? they've been fixed, and are now dynamically loaded!
- changelog history now lets you try to load it again if first attempt failed for whatever reason.
- padding (everywhere) has been slightly reduced to fit in more content and be consistent across ui.
- added more info to the "how to save" popup for ios devices.
- crypto wallet press-to-copy buttons now look like buttons.
- improved ui layout for smallest screens (iphone 5, 5s, se, etc).
- removed partial translations for sake of clarity and consistency.

internal improvements:
- separated web and api servers. they're now completely independent and therefore more stress-resistant.
- added a dedicated script for building the web app if you don't want to reload the frontend server.
- web app building improvements.
- async localization preloading.
- consistent server start time reporting.
- dynamic stream and ip hashing salt generation.

infrastructure improvements:
- load balancing: your api requests are now sent to the least busy server. yes, there are now several of them with more to come in the future.
- when possible, server in closest region is used instead of a far-away one. this should help with download speeds.
- currently there are multiple servers in europe. i will let you know when (and if) i manage to get an american one.

updates for developers and instance hosters:
- server info api endpoint: you can now check up on the api server of choice. it reports all the basic info you may need. [check the api docs](https://github.com/imputnet/cobalt/blob/main/docs/api.md#get-apiserverinfo) for more info.
- api names: each and every api instance should have a distinctive name. this will be useful in the future :)
- added docker compose sample config.
- updated and more granular setup script.
- better api scalability and faster server start up thanks to web and api separation.
- added ability to specify ffmpeg threads. simply add ffmpegThreads to your environment variables!

i'm still in awe from how popular cobalt has become. there are now over 200k of unique users monthly, and that number only keeps growing. i even had to come up with something to accommodate for larger traffic, it's absolutely insane.

love you all, have a great day :D