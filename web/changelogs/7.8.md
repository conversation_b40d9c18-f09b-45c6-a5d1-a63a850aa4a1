---
title: "new years clean up! bug fixes and fresh look for the home page"
date: "December 25, 2023"
banner:
    file: "catroomba.webp"
    alt: "a cat riding a roomba vacuum"
---
merry christmas and happy new year! this update fixes several (very annoying) bugs to help you enjoy your holidays better.

you might have already noticed, but we've refreshed the home page on desktop and mobile! less space wasted, more pleasant to look at. let us know if you like it or not :D

service improvements:
- [#264](https://github.com/imputnet/cobalt/issues/264) anything that includes a period in the url should be possible to download (including instagram stories).
- [#273](https://github.com/imputnet/cobalt/issues/273) soundcloud: falling back to mp3 instead of refusing to download the song at all.
- [#275](https://github.com/imputnet/cobalt/issues/275) youtube: query parameters are parsed and handled correctly, all links should be supported, no matter where v query is located.
- tlds are parsed and validated correctly (e.g. "pinterest.co.uk" works now).
- fixvx.com links are now supported.

interface improvements:
- cleaner and more consistent home page layout.
- cleaned up support section in "about". also includes a link to the status page.

internal improvements:
- urls, subdomains, and tlds are properly validated.
- minor clean up.

changes since 7.7:
- made terms and ethics more descriptive.
- fix only affected twitter videos.
- fixed quick ⌘+V pasting on mac.
- now catching even more youtube-related errors.

this might not seem like a lot, but even smaller changes make a difference!

enjoy this update and the rest of your day :D