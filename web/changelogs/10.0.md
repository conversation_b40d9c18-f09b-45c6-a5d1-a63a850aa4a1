---
title: "cobalt, reborn"
date: "9 Sept, 2024"
banner:
    file: "cobalt10.webp"
    alt: "meowth plush staring into a screen with cobalt 10 ui shown."
---

everything is new! this update marks the start of the latest chapter for cobalt. we spent the entire summer working hard to deliver the best experience ever, and we really hope you enjoy the rebirth of cobalt.

before we list the new features, we also added support for a couple of new services: facebook, bluesky, loom, and snapchat.

cobalt already supports downloading videos from bluesky and will continue to do so after they're officially released. we also added support for saving images from all supported services (such as twitter).

now, here's the gist of what's new in the web app:

- the web app was rebuilt from the ground up with usability & accessibility in mind. everyone will enjoy using the new web app, even if they rely on screen readers or other accessibility tools.
- [on-device remuxing (beta)](/remux). this feature solves all compatibility issues with old software. just drop the file into the remux page, and it'll fix it up to make it work with your favorite apps! vegas pro, logic pro, fl studio, you name it.
- [all-new settings page](/settings). settings are now way easier to understand and use. everything is appropriately labeled, categorized, and described.
- [custom audio bitrate](/settings/audio#audio-bitrate). you can now choose what bitrate to use when processing audio.
- [community instances (beta)](/settings/instances#community), right in the main web app. just go to instance settings and use a custom processing server if you wish. make sure to read the important safety message.
- [tunnel all files (beta)](/settings/privacy#tunnel). this feature will hide your ip address, browser info, and bypass local network restrictions. all downloaded files will also have pretty filenames.
- new localization system. this allowed us to implement a [language picker](/settings/appearance#language) right into cobalt. expect more languages in the future!
- more granular error messages with proper context. no more grouped errors such as "this happened or this or that idk lol guess".
- [settings data management](/settings/advanced#data). you can now export, import, or wipe settings.
- [new donate page](/donate). the donation page has been completely reimagined, with more ways to support us than ever (via stripe and liberapay), and via sharing cobalt with a friend.
- [comprehensive about page](/about). the new about page includes more info than ever before, and we will be progressively adding more content there to make sure there is no more confusion, period.
- convenient updates page (*you're here*). the new updates page is comfortable to read and navigate. we have also moved to using markdown so that we can do *this* and **this** and ~~that~~. it also includes more changelogs than ever before.
- tab key navigation across the entire web app.
- new navigation system with proper routing, history, and all other benefits. it is now persistent and always stays on the screen.
- new dialog system. dialogs now use native html elements, meaning that they work as you'd expect. no more finnicky navigation.
- new picker dialog. pretty, easy to use, and works beautifully on all devices.

...and this is just the tip of the iceberg. we couldn't possibly list all changes. just go and take a look around, don't be scared to press all buttons you see.

and for nerds, we have a giant list of backend changes (that we are also excited about):
- completely restructured API schema and endpoints.
- API now has error codes instead of messages that used to contain HTML, and the error responses also include a separated error context that is much easier to parse.
- server info endpoint returns a lot more contextual information about each instance.
- API and web codebases have been completely separated.
- support for OAuth2 tokens for youtube.
- implemented JWT sessions, which are generated based on a Turnstile challenge or in the future by an API key.
- streams are now tunnels.
- API now returns the filename in the response instead of just as content-disposition for tunnels.
- range requests are now supported for direct tunnels, which means these requests are also pauseable and resumable.
- a ton of refactoring in continuous effort to make the codebase readable for everyone.

this update allows us to actually innovate and develop new & exciting features. we are no longer held back by the legacy codebase. first feature of such kind is on-device remuxing. go check it out!

oh yeah, we now have over 2 million monthly users. kind of insane.

we hope you enjoy this update as much as we enjoyed making it. it was a really fun summer project for both of us.

have a lovely day :D

~ your friends at imput
