---
title: "local processing for everyone, subtitles, audio covers, and more"
date: "30 June, 2025"
banner:
    file: "meowth_sunrise.webp"
    alt: "meowth plush in a forest looking at the rising sun between the trees."
---

it's summertime! even though it's been rainy for us lately, the sun is right on the horizon, just like this cobalt update. we improved local processing, added long-awaited features, and improved a ton of other stuff.

here's what's new since 11.0:

## on-device media processing
local processing is now enabled for everyone by default! it allows for faster downloading, file consistency, and best media compatibility. in this update, we optimized it to work on older browsers, just so no one's missing out on cobalt due to having outdated software.

thanks to local processing, we were able to add **audio covers** in this update. cobalt will automatically add covers/thumbnails from youtube or soundcloud, and it'll be cropped to a square when needed. really cool stuff, and it just works!

please let us know if local processing doesn't work properly on your device, we'll try to improve it!

## video subtitles
we added support for downloading videos with subtitles! in this update, we added full support for subtitles from: `youtube`, `twitter`, `tiktok`, `vimeo`, `loom`, `vk video`, and `rutube`. we'll keep adding support for more services in the future!

to download subtitles, just pick your preferred language in [metadata settings](/settings/metadata#subtitles)! cobalt will add subtitles in this language if they're available.

pro-tip: if you don't need audio, you can save a bit of storage by switching to the "mute" mode on the home page. you'll get a mute video with subtitles and the rest of the metadata!

don't want metadata or subtitles? just [disable metadata](/settings/metadata#metadata) in settings, and cobalt won't add anything.

## youtube downloading
downloading from youtube on the main instance is restored! sorry that it took a bit over a week; we were trying our best to speed it up.

hopefully it'll last for a while, but we think downloading from youtube will get significantly more annoying/complex in next few weeks-months. **right now is the best time to download everything you've been putting off**, either with cobalt or other tools.

**update**: unfortunately it did not last, youtube is unavailable on the main instance again. we will try one more way soon and update this changelog and post about it on socials accordingly.

we're not trying to scare you; it's our educated guess based on what youtube has been doing lately:
- roll out of SABR & related limitations for more clients. SABR is Server ABR, Google's proprietary HLS alternative, controlled by the server.
- growing potoken enforcement.
- various other experiments to restrict "unauthorized access".

we currently have no exact plan on how to handle SABR in cobalt, but we will try to figure it out. for now, we're using youtube clients that don't have it enforced, but we have no clue for how long this will last.

by the way, we also made it possible to [choose any preferred media container](/settings/video#youtube-container) independently from the youtube video codec. could be useful for this occasion!

## general service improvements
- added more metadata to audio files from soundcloud.
- added support for `/groups/` vimeo links.
- added support for ``/v/:id` youtube links.
- added support for new share links from tiktok.
- added support for more vk video links.
- pinterest now returns an appropriate error when a pin is unavailable.
- AI dubs on youtube are no longer accidentally selected as default tracks.
- youtube HLS preference is now deprecated, but can be enabled on a self-hosted instance via `ENABLE_DEPRECATED_YOUTUBE_HLS` in env.

## web app improvements
- improved compatibility of local processing & related code with older browsers.
- disabled multithreading in old mobile safari, making it possible to use local processing on iOS 15+.
- local processing workers:
    - the fetch worker is now less sensitive to network-related errors and returns a descriptive error whenever necessary.
    - the ffmpeg worker now returns an appropriate error when a required stream is missing.
    - the generic crash error is now localized.
    - added a default file icon in case cobalt can't detect the file type.
- made frontend compatible with static cloudflare workers.
- most used languages in [subtitle](/settings/metadata#subtitles) and [audio track](/settings/audio#youtube-dub) dropdowns are now on top.
- default values of subtitle/audio track dropdowns are now localized.
- translated all UI strings to russian.
- fixed overflow in the processing queue.
- updated a bunch of localization strings.
- slightly updated the update notification & fixed its location in RTL layouts.

## processing instance improvements
- fixed HLS downloading from soundcloud that was accidentally broken in the 11.0 update.
- fixed dynamic env reloading.
- added console messages about dynamic env changes.
- `SESSION_RATELIMIT` is now `SESSION_RATELIMIT_MAX`, but the old name remains valid until the next major update. this is a result of a typo in 11.0, sorry!
- `localProcessing` is now `disabled | preferred | forced`, not a boolean. 11.2 accepts boolean values, but this will be removed in a future version.
- added `subtitleLang`, which is any valid ISO 639-1 language code.
- removed backwards compatibility with `twitterGif` and `tiktokH265`.
- updated `local-processing` response in correlation to addition of subtitles and audio covers.
- a lot of refactoring.

for up-to-date info about instance variables, check the docs on github:
- [processing instance environment variables](https://github.com/imputnet/cobalt/blob/main/docs/api-env-variables.md).
- [api documentation](https://github.com/imputnet/cobalt/blob/main/docs/api.md).

## all changes are on github
as usual, you can check [all commits since the 11.0 release on github](https://github.com/imputnet/cobalt/compare/a52dde7...main) for even more details and exact code changes.

we hope that you enjoy this update and have a great rest of your day!

\~ your friends at imput ❤️
