---
title: "vk clips support, improved changelog system, and less bugs"
date: "Sep 11, 2022"
---
new features:
- added support for vk clips. cobalt now lets you download even more cringy videos!
- added update history right to the changelog menu. it's not loaded by default to minimize page load time, but can be loaded upon pressing a button. probably someone will enjoy this.
- as you've just read, cobalt now has on-demand blocks. they're rendered on server upon request and exist to prevent any unnecessary clutter by default. the first feature to use on-demand rendering is history of updates in changelog tab.

changes:
- moved twitter entry to about tab and made it localized.
- added clarity to what services exactly are supported in about tab.

bug fixes:
- cobalt should no longer crash to firefox users if they love to play around with user-agent switching.
- vk videos of any resolution and aspect ratio should now be downloadable.
- vk quality picking has been fixed after vk broke it for parsers on their side.