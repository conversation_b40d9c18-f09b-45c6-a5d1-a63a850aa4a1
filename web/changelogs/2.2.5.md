---
title: "remade localization system once again"
date: "Jul 24, 2022"
---

- new localization system: fast, dynamic, way more organized
- localization strings are WAY more descriptive
- it's now easier to add support for other languages (just one loc file instead of five)
- localization now falls back to english if localized string isnt available
- got rid of all static language selectors (probably)
- slightly updated english and russian strings
- miscellaneous settings items have been bundled together and moved to the bottom, cause they're used the least
- bottom links should no longer touch the popup border on overflow
- rearranged popup order in the rendered page
- bumped version up to 2.2.5

if you see strings that are like this: !!EXAMPLE!! or withoutspace please file an issue on github
