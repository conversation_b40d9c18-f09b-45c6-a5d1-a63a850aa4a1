---
title: "biggest ui refresh yet!"
date: "August 15, 2023"
banner:
    file: "meowthcooking.webp"
    alt: "meowth handling orders in a restaurant"
---
hey! this update is huge and mostly aimed to refresh the ui, but there are also some other nice fixes/additions. read below for more info :)

<span class="text-backdrop">tl;dr:</span>

- entirety of web app has been refreshed. it's more prettier and optimized than ever, both on phone and desktop.
- if you're on ios, try adding cobalt to home screen! it'll look and act like a native app.
- all soundcloud links are now supported and audio quality is higher than before.
- all x (previously twitter) links are now supported and work properly.
- newer reddit videos are downloadable now.
- added some sort of eula, list of keyboard shortcuts, updated privacy policy for more clarity. check it all in refreshed about tab!
- cobalt now lets you know if your browser doesn't support clipboard pasting and helps you fix it.

<span class="text-backdrop">accessibility notice:</span>
this update includes animations and transparency, if you'd like to disable any or all of them, head to settings > other > accessibility.

<span class="text-backdrop">[full changelog]</span>

service improvements:
- fixed unexpected 502 errors when downloading newer reddit videos.
- newer reddit videos (with audio) are downloadable now.
- upgraded soundcloud downloads to use higher audio quality than before.
- all soundcloud links are now supported.
- added support for x.com urls.
- changed twitter api once again. now everything works, again.

web improvements:
- all-new matte glass aesthetic, applied to revamped popup headers, tab selectors, and also small popups.
- rounded corners everywhere! cobalt is now safe for everyone who can't handle sharp objects.
- paddings everywhere are smaller, more content fits on the screen at once.
- optimized installed web app to look and act like a native app, especially on ios.
- added update release dates to changelogs.
- cobalt now lets you know if your browser doesn't support clipboard api and helps you fix it.
- refreshed all popups: less padding, more content.
- completely remade error and download popups, they're consistent with the rest of refreshed design.
- refreshed the look of entire changelog tab: separated title and version/commit, made title bigger, evened out all paddings.
- replaced close button with back button, moved it to left.
- added interaction animations.
- added more keyboard shorcuts.
- added a list of keyboard shortcuts to about tab.
- added eula to about tab. check it out.
- added more accessibility options, put them all into one category. you can disable animations and transparency if you want to.
- added a link to self-troubleshooting guide to about tab.
- renamed 2160p and 4320p to 4k and 8k respectfully for better clarity.
- popups now work without any weird workarounds, especially on mobile. they're clean and nice.
- home screen now also works without any weird workarounds. it is also clean and nice.
- optimized css of almost all ui elements. should be even more consistent across platforms now.
- added ability to translate "cobalt" more in-depth localization. for example, in russian "cobalt" is now "кобальт", that's the style i'll be going with from now on.
- updated many localization strings for more clarity.
- removed ability to change the app name dynamically in all locations. cobalt is a sustained app name.
- updated donation and privacy policy texts for more clarity in both english and russian.
- home screen now smoothly fades in instead of popping in.
- proper banner loading. no more jumping text!
- proper banner error handling. if banner wasn't loaded, it'll simply go grey instead of disappearing.
- links are no longer italic and are instead underlined.
- collapsible lists now have corresponding emoji.
- donate button is now highlighted with magenta instead of white.
- proper dropdown arrow.
- removed 6.0 api fallback.
- fixed celebrations emoji. again.
- cleaned up all related frontend modules, especially page.js.
- urgent notice is now a js element, not a static piece of text. can be updated easily.

api improvements:
- now catching all json api related errors.
- moved on demand blocks to web server, now changelog can be updated independently from preferred api server.
- now sending standard rate limiting headers.
- better readability in source.

other improvements:
- renamed docker-compose.yml.example to docker-compose.example.yml for linting in code editors.
- added a wiki with wip troubleshooting guide on github. more guides are coming soon!

that's a ton of changes! i really hope you like this update as much as i do.

if you experience any issues, feel free to contact me on any platform listed in about tab! i'd love to hear back from you.

thank you for sticking with me and cobalt, i hope you have THE best day :D