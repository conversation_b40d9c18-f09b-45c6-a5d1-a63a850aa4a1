---
title: "now helping over 1 million people monthly"
date: "May 17, 2024"
banner:
    file: "millionusers.webp"
    alt: "collage of two photos, side by side. left photo: brown cake with 7 lit candles forming 1000000 and one ferrero rocher candy in the middle with cobalt (double greater than symbol) logo on it. right photo: chocolate cake with 7 lit candles forming 1000000 and cobalt logo formed with whipped cream on the cake. two plushes of meowth and pompompurin in party hats are seen behind the cake."
---
yesterday, cobalt hit 1 million users around the world! it's an absolutely insane milestone for us and we're incredibly grateful to everyone saving and creating what they love with help of cobalt. thank you for being our friends.

in anticipation of 7 figure user count, we've revamped the cobalt codebase and infrastructure to be faster and more reliable than ever. a combination of many changes has resulted into incredible download speeds (up to 30 MB/s, as tested by both developers in europe).

note: there's no backend instance in asia just yet, so if you're there, you might experience average speeds *for now*. you can help us afford a dedicated server in asia by donating to cobalt in the "donate" menu.

<span class="text-backdrop">changes since the last major update</span>

service improvements:
- youtube music support on the main instance is back!
- added support for pinterest images and gifs.
- cobalt will now use original soundcloud mp3 file when available.
- fixed a youtube bug that prevented some videos from downloading.

ui/ux improvements:
- cobalt web app is now fully optimized for ipad. you can add it to home screen from share menu to make it act like a native app!
- majorly reduced vertical padding when viewing cobalt in mobile web browser, allowing for more content at once. most noticeable on smaller screens.
- status bar color is now dynamic in the web browser on ios and web app on android.
- web app on android feels way more native than before.
- filename style icons are no longer blurry in safari.
- changelog notification no longer overlaps with dynamic island on newer iphones when cobalt is installed as a web app.
- fixed safe area padding.

other changes:
- added support for [freebind](https://github.com/imputnet/freebind.js), made by one of the cobalt developers.
- rate limit and max video length limits are now customizable through [environment variables](https://github.com/imputnet/cobalt/blob/main/docs/run-an-instance.md#variables-for-api).
- cobalt api now returns rate limit headers at all times.
- majorly cleaned up the codebase: removed unnecessary functions, rewrote those that were cryptic and confusing. it's way more comprehensible and contribution-friendly than ever before.
- moved the [cobalt repo](https://github.com/imputnet/cobalt) to our organization on github. everything stayed the same and all old links link back to it.

note for instance hosters:
along with cobalt repo, the docker image also moved! please update the url for it in your config along with watchtower args to include restarting containers (just in case) as seen in [updated docker compose example](https://github.com/imputnet/cobalt/blob/main/docs/examples/docker-compose.example.yml). we're mirroring packages to old url for now, but it won't last forever.

that's it for now! hope you have an amazing day and share the 1 million celebration with us :)

join our [discord server](https://discord.gg/pQPt8HBUPu) to discuss everything cobalt there