---
title: "fastest one in the game"
date: "Mar 24, 2023"
banner:
    file: "catspeed.webp"
    alt: "a cat running very fast in an exercise wheel"
---
hey, notice anything different? well, at very least the page loaded way faster! this update includes many improvements and fixes, but also some new features.

<span class="text-backdrop">tl;dr:</span>

- twitter retweet links are now supported.
- all vimeo videos should now be possible to download.
- you now can download audio from vimeo.
- it's now possible to pick between preferred vimeo download method in settings.
- fixed issues related to tiktok, twitter, twitter spaces, and vimeo downloads.
- overall cobalt performance should be MUCH better.

service improvements:
- added support for twitter retweet links. now all kinds of tweet links are supported.
- fixed the issue related to periods in tiktok usernames (#96).
- fixed twitter spaces downloads.
- added support for audio downloads from vimeo.
- added ability to choose between "progressive" and "dash" vimeo downloads. go to settings > video to pick your preference.
- fixed the issue related to vimeo quality picking.
- fixed the issue when vimeo module wouldn't show appropriate errors and instead would fallback to default ones.
- improved audio only downloads for some edge cases.
- (hopefully) better youtube reliability.
- temporarily disabled douyin support due to api endpoint cut off.

interface improvements:
- merged clipboard and mode switcher rows into one for mobile view.
- added left-handed layout toggle for those who prefer to have the clipboard button on left.
- new custom-made clipboard icon. now it clearly indicates what it does.
- improved english and russian localization. both are way more direct and less bloaty.
- frontend page is now rendered once and is cached on disk instead of being rendered every time someone requests a page. this greatly improves page loading speeds and further reduces strain put on the server.
- frontend page is now minimized just like js and css files. this should minimize traffic wasted on loading the page, along with minor loading speed improvement.
- added proper checkbox icon for better clarity.
- checkboxes are now stretched edge-to-edge on phone to be easier to manage for right-handed people.
- removed button hover highlights on phones.
- fixed button press animations for safari on ios.
- fixed text selection on ios. previously you could select text or images anywhere, but now they're selectable in limited places, just like on other platforms.
- frontend platform is now marked in settings: p is for pc; m is for mobile; i is for ios. this is done for possible future debugging and issue-solving.
- better error messaging.

internal improvements:
- better rate limiting, there should be way less cases of accidental limits.
- added support for m3u8 playlists. this will be useful for future additions, and is currently used by vimeo module.
- added support for "chop" stream format for vimeo downloads.
- fixed vk user id extraction. i assumed the - in url was a separator, but it's actually a part of id.
- completely reworked the vimeo module. it's much cleaner and better performant now.
- minor clean ups across the board.

not really related to this update, but thank you for 50k monthly users! i really appreciate that you're still here, because that means i'm doing some things right :D