<script lang="ts">
    import env from "$lib/env";
    import { t } from "$lib/i18n/translations";

    import SectionHeading from "$components/misc/SectionHeading.svelte";
</script>

<section id="general">
<SectionHeading
    title={$t("about.heading.general")}
    sectionId="general"
/>

политика конфиденциальности кобальта проста: мы ничего не собираем и не храним о
тебе. то, что ты делаешь, — это исключительно твоё дело, а не наше или чьё-либо
ещё.

эти условия применяются только при использовании официального инстанса кобальта.
в других случаях, возможно, придётся обратиться к хостеру инстанса за точной
информацией.
</section>

<section id="local">
<SectionHeading
    title={$t("about.heading.local")}
    sectionId="local"
/>

инструменты, которые используют обработку на устройстве, работают офлайн,
локально и никогда никуда не отправляют обработанные данные. они явно помечены
как таковые, когда это применимо.
</section>

<section id="saving">
<SectionHeading
    title={$t("about.heading.saving")}
    sectionId="saving"
/>

при использовании функции сохранения, кобальту может понадобиться проксировать
или ремуксировать/транскодировать файлы. если это так, то для этой цели
создаётся временный туннель, и минимально необходимая информация о медиа
хранится в течение 90 секунд.

на неизменённом и официальном инстансе кобальта **все данные туннеля шифруются
ключом, к которому имеет доступ только конечный пользователь**.

зашифрованные данные туннеля могут включать:
- название исходного сервиса.
- исходные ссылки на медиафайлы.
- необходимые внутренние аргументы для различения типов обработки.
- ключевые метаданные файла (сгенерированное имя, заголовок, автор, год
  создания, данные об авторских правах).
- минимальная информация об исходном запросе, которая может быть использована
  для восстановления туннеля после ошибки ссылки во время скачивания.

эти данные безвозвратно удаляются из оперативной памяти сервера через 90 секунд.
никто не имеет доступа к кэшированным данным туннеля, даже владельцы инстансов,
если исходный код кобальта не изменён.

медиаданные из туннелей нигде не хранятся/кэшируются. всё обрабатывается в
реальном времени, даже при ремуксинге и транскодировании. туннели кобальта
работают как анонимный прокси.

если твоё устройство поддерживает локальную обработку, то зашифрованный туннель
содержит намного меньше информации, потому что она возвращается клиенту.

смотри [соответствующий исходный код на
github](https://github.com/imputnet/cobalt/tree/main/api/src/stream), чтобы
узнать больше о том, как это работает.
</section>

<section id="encryption">
<SectionHeading
    title={$t("about.heading.encryption")}
    sectionId="encryption"
/>

временно хранящиеся данные туннеля шифруются с использованием стандарта AES-256.
ключи расшифровки включены только в ссылку доступа и никогда не
логируются/кэшируются/хранятся где-либо. только конечный пользователь имеет
доступ к ссылке и ключам шифрования. ключи генерируются уникально для каждого
запрошенного туннеля.
</section>

{#if env.PLAUSIBLE_ENABLED}
<section id="plausible">
<SectionHeading
    title={$t("about.heading.plausible")}
    sectionId="plausible"
/>

мы используем [plausible](https://plausible.io/), чтобы знать приблизительное
число активных пользователей кобальта, полностью анонимно. никакая
идентифицирующая информация о тебе или твоих запросах никогда не хранится. все
данные анонимизированы и агрегированы. мы сами хостим и управляем [инстансом
plausible](https://{env.PLAUSIBLE_HOST}/), который использует кобальт.

plausible не использует куки и полностью соответствует GDPR, CCPA и PECR.

если ты хочешь отказаться от анонимной аналитики, то это можно сделать в
[настройках приватности](/settings/privacy#analytics). после отказа скрипт
plausible не будет загружаться.

[узнай больше о преданности plausible к
приватности](https://plausible.io/privacy-focused-web-analytics).
</section>
{/if}

<section id="cloudflare">
<SectionHeading
    title={$t("about.heading.cloudflare")}
    sectionId="cloudflare"
/>

мы используем сервисы cloudflare для:
- защиты от ddos и абьюза.
- защиты от ботов (cloudflare turnstile).
- хостинга и деплоя статического веб-приложения (cloudflare workers).

всё это необходимо для обеспечения лучшего опыта для всех. cloudflare — наиболее
приватный и надёжный провайдер всех упомянутых решений из всех известных нам
провайдеров.

cloudflare полностью соответствует требованиям GDPR и HIPAA.

[узнай больше о преданности cloudflare к
приватности](https://www.cloudflare.com/trust-hub/privacy-and-data-protection/).
</section>
