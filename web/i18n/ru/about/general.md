<script lang="ts">
    import { t } from "$lib/i18n/translations";
    import { contacts, docs } from "$lib/env";

    import SectionHeading from "$components/misc/SectionHeading.svelte";
</script>

<section id="summary">
<SectionHeading
    title={$t("about.heading.summary")}
    sectionId="summary"
/>

кобальт помогает сохранять что угодно с твоих любимых сайтов: видео, аудио, фото
или гифки. просто вставь ссылку и вперёд!

никакой рекламы, трекеров, платных подписок и прочей ерунды. просто удобное
веб-приложение, которое работает где угодно и когда угодно.
</section>

<section id="motivation">
<SectionHeading
    title={$t("about.heading.motivation")}
    sectionId="motivation"
/>

кобальт был создан для всеобщего блага, чтобы защитить людей от рекламы и
вредоносных программ, которые навязывают альтернативные загрузчики. мы верим,
что лучший софт — безопасный, открытый и доступный. все проекты imput следуют
этим принципам.
</section>

<section id="privacy-efficiency">
<SectionHeading
    title={$t("about.heading.privacy_efficiency")}
    sectionId="privacy-efficiency"
/>

все запросы к бэкенду анонимны, и вся инфа о потенциальных файловых туннелях
зашифрована. у нас строгая политика нулевых логов, мы *никогда* не храним
идентифицирующую инфу о людях и никого не отслеживаем.

если запрос требует дополнительной обработки, например ремукса или
транскодирования, то кобальт обрабатывает медиафайлы прямо на твоём устройстве.
это обеспечивает максимальную эффективность и приватность.

если твоё устройство не поддерживает локальную обработку, то вместо неё
используется серверная обработка в реальном времени. в этом сценарии
обработанные медиаданные передаются напрямую клиенту, никогда не сохраняясь на
диске сервера.

ты можешь [включить принудительное туннелирование](/settings/privacy#tunnel),
чтобы ещё сильнее повысить приватность. когда оно включено, кобальт будет
туннелировать все скачиваемые файлы, а не только те, которым это необходимо.
никто не узнает, откуда и что ты скачиваешь, даже твой провайдер. всё, что они
увидят, это то, что ты используешь инстанс кобальта.
</section>

<section id="community">
<SectionHeading
    title={$t("about.heading.community")}
    sectionId="community"
/>

кобальт используют бесчисленные артисты, преподаватели и прочие создатели
контента, чтобы заниматься любимым делом. мы всегда на связи с нашим сообществом
и работаем вместе, чтобы делать кобальт ещё полезнее. не стесняйся
[присоединиться к разговору](/about/community)!

мы верим, что будущее интернета — открытое и свободное, поэтому кобальт
опубликован с [открытым исходным кодом](https://sourcefirst.com/) и его можно
легко [захостить самому]({docs.instanceHosting}).

если твой друг хостит инстанс обработки, просто попроси у него домен и [добавь
его в настройках инстанса](/settings/instances#community).

ты можешь посмотреть исходный код и внести свой вклад [на
github]({contacts.github}) в любое время. мы рады любым предложениям и помощи!
</section>
