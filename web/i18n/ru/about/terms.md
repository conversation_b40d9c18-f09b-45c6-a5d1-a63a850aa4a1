<script lang="ts">
    import { t } from "$lib/i18n/translations";
    import SectionHeading from "$components/misc/SectionHeading.svelte";
</script>

<section id="general">
<SectionHeading
    title={$t("about.heading.general")}
    sectionId="general"
/>

эти условия применяются только при использовании официального инстанса кобальта.
в других случаях, возможно, придётся обратиться к хостеру инстанса за точной
информацией.
</section>

<section id="saving">
<SectionHeading
    title={$t("about.heading.saving")}
    sectionId="saving"
/>

функция сохранения упрощает скачивание контента из интернета, и мы не несём
никакой ответственности за то, как будет использоваться сохранённый контент.

серверы обработки работают как продвинутые прокси и никогда не записывают
запрошенный контент на диск. всё происходит в оперативной памяти и полностью
удаляется после завершения туннеля. у нас нет логов загрузок, и мы не можем
никого идентифицировать.

подробнее о том, как работают туннели, можно узнать в [политике
конфиденциальности](/about/privacy).
</section>

<section id="responsibility">
<SectionHeading
    title={$t("about.heading.responsibility")}
    sectionId="responsibility"
/>

ты (конечный пользователь) несёшь ответственность за то, что делаешь с нашими
инструментами, как используешь и распространяешь полученный контент. пожалуйста,
уважай чужой труд и всегда указывай авторов. убедись, что ты не нарушаешь
никаких условий или лицензий.

при использовании в образовательных целях всегда ссылайся на источники и
указывай авторов.

добросовестное использование и указание авторства приносят пользу всем.
</section>

<section id="abuse">
<SectionHeading
    title={$t("about.heading.abuse")}
    sectionId="abuse"
/>

у нас нет возможности автоматически выявлять злоупотребления, так как кобальт
полностью анонимен. однако, есть возможность сообщить нам о такой деятельности
по почте, и мы сделаем всё возможное, чтобы принять нужные меры вручную:
abuse[at]imput.net

**этот адрес не предназначен для поддержки пользователей. ты не получишь ответ,
если твой запрос не связан со злоупотреблениями.**

если у тебя возникли проблемы с работой кобальта, то ты можешь обратиться за
помощью любым удобным способом на [странице поддержки и
сообщества](/about/community).
</section>
