{"theme.auto": "авто", "theme.light": "светлая", "audio.bitrate.kbps": "кб/с", "theme.dark": "тёмная", "audio.youtube.dub": "звуковая дорожка youtube", "video.quality.max": "8k+", "page.video": "видео", "page.audio": "аудио", "video.quality.1440": "1440p", "video.quality.1080": "1080p", "video.quality.720": "720p", "video.quality.480": "480p", "video.quality.360": "360p", "video.quality.240": "240p", "video.quality.144": "144p", "metadata.file": "метаданные файла", "saving.title": "метод сохранения", "saving.ask": "спросить", "saving.download": "скачать", "saving.share": "поделиться", "saving.copy": "скопировать", "language": "язык", "language.preferred.title": "предпочитаемый язык", "privacy.analytics": "анонимная аналитика трафика", "audio.tiktok.original.title": "скачивать оригинальный звук", "privacy.tunnel": "туннелирование", "privacy.tunnel.title": "всегда туннелировать файлы", "audio.format.mp3": "mp3", "audio.format.ogg": "ogg", "audio.format.wav": "wav", "audio.format.opus": "opus", "page.privacy": "приватность", "theme": "тема", "video.quality": "качество видео", "video.twitter.gif": "twitter/x", "video.quality.2160": "4k", "audio.format": "формат аудио", "audio.bitrate": "битрейт аудио", "audio.tiktok.original": "tiktok", "metadata.disable.title": "отключить метаданные", "language.auto.title": "автоматический выбор", "metadata.disable.description": "название, исполнитель и другая информация не будут добавлены в файл.", "language.preferred.description": "этот язык будет использоваться когда автоматический выбор отключен. любой непереведённый текст будет отображаться на английском языке.\n\nмы используем переводы, предоставленные сообществом. они могут быть неточными или неполными.", "audio.youtube.dub.description": "cobalt будет использовать дублированную аудиодорожку для выбранного языка, если она доступна. в противном случае будет использоваться оригинальная.", "language.auto.description": "если доступен перевод, то кобальт будет использовать язык твоего браузера. в ином случае будет использоваться английский.", "theme.description": "авто тема переключается между светлой и тёмной темой в зависимости от системной темы.", "page.debug": "инфа для гиков", "page.appearance": "внешний вид", "page.instances": "инстансы", "page.advanced": "продвинутые", "page.accessibility": "общедоступность", "page.metadata": "метаданные", "page.local": "локальная обработка", "video.youtube.codec": "предпочитаемый кодек для youtube", "audio.youtube.dub.title": "предпочитаемый язык озвучки", "metadata.filename.basic": "базовый", "video.twitter.gif.title": "конвертировать зацикленные видео в GIF", "metadata.filename.description": "стиль названий файлов используется только для файлов, туннелированных через кобальт. некоторые сервисы поддерживают только классический стиль.", "youtube.dub.original": "оригинальный", "metadata.filename.pretty": "красивый", "metadata.filename.nerdy": "занудный", "audio.tiktok.original.description": "кобальт будет скачивать оригинальный звук из видео без каких-либо изменений от автора поста.", "metadata.filename": "стиль названий файлов", "metadata.filename.classic": "классический", "video.twitter.gif.description": "GIF конвертация неэффективна, финальный файл может быть огромным и в плохом качестве.", "audio.youtube.better_audio.title": "предпочитать лучшее качество", "audio.format.description": "все форматы кроме \"лучшего\" конвертируются из исходного формата, поэтому возможна небольшая потеря качества. когда выбран \"лучший\" формат, аудио остаётся в оригинальном формате, если это возможно.", "audio.youtube.better_audio.description": "кобальт будет пытаться выбрать самое качественное аудио в режиме скачивания аудио. оно может быть недоступно в зависимости от ответа youtube, текущей нагрузки и состояния сервера. на кастомных инстансах эта опция может не поддерживаться.", "audio.youtube.better_audio": "качество аудио с youtube", "video.quality.description": "если предпочитаемое качество недоступно, то выбирается следующий лучший вариант.", "video.youtube.codec.description": "h264: наилучшая совместимость, среднее качество. максимальное качество — 1080p.\nav1: наилучшее качество и сжатие. поддерживает 8k и HDR.\nvp9: то же качество, что и у av1, но файл в ~2x больше. поддерживает 4k & HDR.\n\nav1 и vp9 не очень широко поддерживаются, возможно придётся использовать дополнительное ПО для их проигрывания/обработки. кобальт выбирает следующий лучший кодек, если предпочитаемый недоступен.", "audio.bitrate.description": "битрейт применяется только при конвертации аудио в формат с потерями. кобальт не может улучшить качество исходного аудио, поэтому выбор битрейта выше 128 кб/с может увеличить размер файла без заметной разницы в звуке. воспринимаемое качество может различаться в зависимости от формата.", "video.h265": "high efficiency video codec", "video.h265.title": "использовать h265 для видео", "video.h265.description": "позволяет скачивать видео с tiktok и xiaohongshu в более высоком качестве, но с потерей совместимости.", "video.youtube.hls": "форматы hls для youtube", "video.youtube.hls.description": "в этом режиме доступны только кодеки h264 и vp9. оригинальный аудио кодек aac перекодируется для совместимости, поэтому качество аудио может быть хуже чем у варианта без HLS.\n\nэта функция экспериментальна, поэтому может быть убрана или изменена в будущем.", "audio.format.best": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "video.youtube.hls.title": "предпочитать hls для видео и аудио", "metadata.filename.preview.video": "Название Видео - Автор Видео", "metadata.filename.preview.audio": "Название Аудио - Автор Аудио", "filename.preview_desc.video": "превью видео файла", "filename.preview_desc.audio": "превью аудио файла", "saving.description": "предпочтительный способ сохранения файла или ссылки с кобальта. если предпочитаемый метод недоступен или что-то пойдёт не так, кобальт спросит тебя как поступить.", "accessibility.transparency.description": "уменьшает прозрачность поверхностей и выключает эффекты размытия. также может улучшить работу интерфейса на менее мощных устройствах.", "accessibility.transparency.title": "уменьшить визуальную прозрачность", "accessibility.visual": "интерфейс", "accessibility.haptics": "вибрация", "accessibility.behavior": "поведение", "accessibility.auto_queue.description": "очередь обработки не будет открываться автоматически при добавлении новой задачи. прогресс всё равно будет отображаться, и ты всё равно сможешь открыть её вручную.", "privacy.analytics.learnmore": "узнай больше о преданности plausible к приватности.", "accessibility.motion.description": "анимации и переходы будут отключены, когда это возможно.", "accessibility.haptics.title": "отключить вибрацию", "accessibility.haptics.description": "вся вибрация будет отключена.", "accessibility.auto_queue.title": "не открывать очередь обработки", "privacy.analytics.description": "анонимная аналитика трафика нужна, чтобы знать приблизительное количество активных пользователей кобальта. идентифицирующая информация о тебе никогда не сохраняется. все обрабатываемые данные анонимизированы и агрегированы.\n\nмы используем собственный инстанс plausible, который не использует куки и полностью соответствует требованиям GDPR, CCPA и PECR.", "privacy.tunnel.description": "cobalt скроет твой ip адрес, информацию о браузере и обойдёт местные сетевые ограничения. когда включено, у всех файлов будут читаемые названия вместо абракадабры.", "accessibility.motion.title": "уменьшить движение", "privacy.analytics.title": "не участвовать в аналитике", "advanced.debug": "отладка", "advanced.debug.description": "даёт доступ к странице с различной информацией, которая может быть полезна для отладки. никак не меняет поведение кобальта.", "advanced.debug.title": "включить функции для зануд", "processing.community": "инстансы сообщества", "processing.enable_custom.description": "кобальт будет использовать сторонний инстанс обработки, если ты так решишь. несмотря на то, что у кобальта есть некоторые меры безопасности, мы не несём ответственности за любой ущерб, причинённый сторонним инстансом, так как мы его не контролируем.\n\nбудь осторожен с тем, какие инстансы ты используешь, и убедись, что их хостят люди, которым ты доверяешь.", "processing.enable_custom.title": "использовать сторонний инстанс", "local.saving": "локальная обработка медиа", "local.saving.description": "при скачивании медиа, ремуксинг и транскодирование будут выполняться на устройстве, а не в облаке. ты увидишь подробный прогресс в очереди обработки.\n\nникогда: локальная обработка не будет использоваться. инстансы обработки могут принудительно включать эту функцию, поэтому эта опция может не иметь эффекта.\nиногда: медиафайлы, требующие дополнительной обработки, будут загружаться через очередь обработки, но остальные медиафайлы будут загружаться менеджером загрузок твоего браузера.\nвсегда: все медиафайлы всегда будут проксироваться и загружаться через очередь обработки.\n\nэксклюзивные функции на устройстве не зависят от этой настройки, они всегда работают локально.", "advanced.settings_data": "данные настроек", "local.webcodecs.description": "при декодировании или кодировании файлов кобальт будет пытаться использовать webcodecs. эта функция позволяет обрабатывать медиафайлы с ускорением на GPU, так что всё декодирование и кодирование будет намного быстрее.\n\nдоступность и стабильность этой функции зависят от возможностей твоего устройства и браузера. что-то может сломаться или работать некорректно.", "processing.access_key": "ключ доступа к инстансу", "advanced.local_storage": "локальное хранилище", "local.webcodecs": "webcodecs", "local.webcodecs.title": "использовать webcodecs для локальной обработки", "processing.access_key.title": "использовать ключ доступа", "processing.custom_instance.input.alt_text": "домен стороннего инстанса", "tabs": "навигация", "tabs.hide_remux": "скрыть страницу ремукса", "tabs.hide_remux.description": "если ты не пользуешься ремуксом, то его можно скрыть из панели навигации.", "processing.access_key.description": "кобальт будет использовать этот ключ для запросов к инстансу обработки вместо других методов аутентификации. убедись, что инстанс поддерживает api ключи!", "processing.access_key.input.alt_text": "ключ доступа u-u-i-d", "video.youtube.container": "контейнер файла для youtube", "video.youtube.container.description": "когда выбран \"авто\" контейн<PERSON><PERSON>, кобальт автоматически подберёт оптимальный контейнер в зависимости от выбранного кодека: mp4 для h264; webm для vp9/av1.", "subtitles.description": "кобальт добавит субтитры к скачанному файлу на предпочитаемом языке, если они доступны.\n\nнекоторые сервисы не имеют выбора языка, и в таком случае кобальт добавит единственную доступную дорожку субтитров, если выбран любой язык.", "subtitles": "субтитры", "subtitles.title": "язык субтитров", "subtitles.none": "никакой", "local.saving.disabled": "никогда", "local.saving.preferred": "иногда", "local.saving.forced": "всегда"}