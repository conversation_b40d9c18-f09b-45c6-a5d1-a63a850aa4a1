{"auth.jwt.missing": "couldn't authenticate with the processing instance because the access token is missing. try again in a few seconds or reload the page!", "auth.jwt.invalid": "couldn't authenticate with the processing instance because the access token is invalid. try again in a few seconds or reload the page!", "auth.turnstile.missing": "couldn't authenticate with the processing instance because the captcha solution is missing. try again in a few seconds or reload the page!", "auth.turnstile.invalid": "couldn't authenticate with the processing instance because the captcha solution is invalid. try again in a few seconds or reload the page!", "auth.key.missing": "an access key is required to use this processing instance but it's missing. add it in instance settings!", "auth.key.not_api_key": "an access key is required to use this processing instance but it's missing. add it in instance settings!", "auth.key.invalid": "the access key is invalid. reset it in instance settings and use a proper one!", "auth.key.not_found": "the access key you used couldn't be found. are you sure this instance has your key?", "auth.key.invalid_ip": "your ip address couldn't be parsed. something went very wrong. report this issue!", "auth.key.ip_not_allowed": "your ip address is not allowed to use this access key. use a different instance or network!", "auth.key.ua_not_allowed": "your user agent is not allowed to use this access key. use a different client or device!", "unreachable": "couldn't connect to the processing instance. check your internet connection and try again!", "timed_out": "the processing instance took too long to respond. it may be overwhelmed at the moment, try again in a few seconds!", "rate_exceeded": "you're making too many requests. try again in {{ limit }} seconds.", "capacity": "cobalt is at capacity and can't process your request at the moment. try again in a few seconds!", "generic": "something went wrong and i couldn't get anything for you, try again in a few seconds. if the issue sticks, please report it!", "unknown_response": "couldn't read the response from the processing instance. this is probably caused by the web app being out of date. reload the app and try again!", "invalid_body": "couldn't send the request to the processing instance. this is probably caused by the web app being out of date. reload the app and try again!", "service.unsupported": "this service is not supported yet. have you pasted the right link?", "service.disabled": "this service is generally supported by cobalt, but it's disabled on this processing instance. try a link from another service!", "service.audio_not_supported": "this service doesn't support audio extraction. try a link from another service!", "link.invalid": "your link is invalid or this service is not supported yet. have you pasted the right link?", "link.unsupported": "{{ service }} is supported, but i couldn't recognize your link. have you pasted the right one?", "fetch.fail": "something went wrong when fetching info from {{ service }} and i couldn't get anything for you. if this issue sticks, please report it!", "fetch.critical": "the {{ service }} module returned an error that i don't recognize. try again in a few seconds, but if this issue sticks, please report it!", "fetch.critical.core": "one of the core modules returned an error that i don't recognize. try again in a few seconds, but if this issue sticks, please report it!", "fetch.empty": "couldn't find any media that i could download for you. are you sure you pasted the right link?", "fetch.rate": "the processing instance got rate limited by {{ service }}. try again in a few seconds!", "fetch.short_link": "couldn't get info from the short link. are you sure it works? if it does and you still get this error, please report this issue!", "content.too_long": "media you requested is too long. the duration limit on this instance is {{ limit }} minutes. try something shorter instead!", "content.video.unavailable": "i can't access this video. it may be restricted on {{ service }}'s side. try a different link!", "content.video.live": "this video is currently live, so i can't download it yet. wait for the live stream to finish and try again!", "content.video.private": "this video is private, so i can't access it. change its visibility or try another one!", "content.video.age": "this video is age-restricted, so i can't access it anonymously. try again or try a different link!", "content.video.region": "this video is region locked, and the processing instance is in a different location. try a different link!", "content.region": "this content is region locked, and the processing instance is in a different location. try a different link!", "content.paid": "this content requires purchase. cobalt can't download paid content. try a different link!", "content.post.unavailable": "couldn't find anything about this post. its visibility may be limited or it may not exist. make sure your link works and try again in a few seconds!", "content.post.private": "couldn't get anything about this post because it's from a private account. try a different link!", "content.post.age": "this post is age-restricted, so i can't access it anonymously. try again or try a different link!", "youtube.no_matching_format": "youtube didn't return any acceptable formats. cobalt may not support them or they're re-encoding on youtube's side. try again a bit later, but if this issue sticks, please report it!", "youtube.decipher": "youtube updated its decipher algorithm and i couldn't extract the info about the video. try again in a few seconds, but if this issue sticks, please report it!", "youtube.login": "couldn't get this video because youtube asked the processing instance to prove that it's not a bot. try again in a few seconds, but if it still doesn't work, please report this issue!", "youtube.token_expired": "couldn't get this video because the youtube token expired and wasn't refreshed. try again in a few seconds, but if it still doesn't work, please report this issue!", "youtube.no_hls_streams": "couldn't find any matching HLS streams for this video. try downloading it without HLS!", "youtube.api_error": "youtube updated something about its api and i couldn't get any info about this video. try again in a few seconds, but if this issue sticks, please report it!", "youtube.temporary_disabled": "youtube downloading is temporarily disabled due to restrictions from youtube's side. we're already looking for ways to go around them.\n\nwe apologize for the inconvenience and are doing our best to restore this functionality. check cobalt's socials or github for timely updates!", "youtube.drm": "this youtube video is protected by widevine DRM, so i can't download it. try a different link!", "youtube.no_session_tokens": "couldn't get required session tokens for youtube. this may be caused by a restriction on youtube's side. try again in a few seconds, but if this issue sticks, please report it!"}