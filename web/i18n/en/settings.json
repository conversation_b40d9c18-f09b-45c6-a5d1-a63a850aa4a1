{"page.appearance": "appearance", "page.privacy": "privacy", "page.video": "video", "page.audio": "audio", "page.metadata": "metadata", "page.advanced": "advanced", "page.debug": "info for nerds", "page.instances": "instances", "page.local": "local processing", "page.accessibility": "accessibility", "theme": "theme", "theme.auto": "auto", "theme.light": "light", "theme.dark": "dark", "theme.description": "auto theme switches between light and dark themes depending on your device's display mode.", "video.quality": "video quality", "video.quality.max": "8k+", "video.quality.2160": "4k", "video.quality.1440": "1440p", "video.quality.1080": "1080p", "video.quality.720": "720p", "video.quality.480": "480p", "video.quality.360": "360p", "video.quality.240": "240p", "video.quality.144": "144p", "video.quality.description": "if preferred video quality isn't available, next best is picked instead.", "video.youtube.codec": "preferred youtube video codec", "video.youtube.codec.description": "h264: best compatibility, average quality. max quality is 1080p. \nav1: best quality and efficiency. supports 8k & HDR. \nvp9: same quality as av1, but file is ~2x bigger. supports 4k & HDR.\n\nav1 and vp9 aren't widely supported, you might have to use additional software to play/edit them. cobalt picks next best codec if preferred one isn't available.", "video.youtube.container": "youtube file container", "video.youtube.container.description": "when \"auto\" is selected, cobalt will pick the best container automatically depending on selected codec: mp4 for h264; webm for vp9/av1.", "video.youtube.hls": "youtube hls formats", "video.youtube.hls.title": "prefer hls for video & audio", "video.youtube.hls.description": "only h264 and vp9 codecs are available in this mode. original audio codec is aac, it's re-encoded for compatibility, audio quality may be slightly worse than the non-HLS counterpart.\n\nthis option is experimental, it may go away or change in the future.", "video.twitter.gif": "twitter/x", "video.twitter.gif.title": "convert looping videos to GIF", "video.twitter.gif.description": "GIF conversion is inefficient, converted file may be obnoxiously big and low quality.", "video.h265": "high efficiency video codec", "video.h265.title": "allow h265 for videos", "video.h265.description": "allows downloading videos from platforms like tiktok and xiaohongshu in higher quality at cost of compatibility.", "audio.format": "audio format", "audio.format.best": "best", "audio.format.mp3": "mp3", "audio.format.ogg": "ogg", "audio.format.wav": "wav", "audio.format.opus": "opus", "audio.format.description": "all formats but \"best\" are converted from the source format, there will be some quality loss. when \"best\" format is selected, the audio is kept in its original format whenever possible.", "audio.bitrate": "audio bitrate", "audio.bitrate.kbps": "kb/s", "audio.bitrate.description": "bitrate is applied only when converting audio to a lossy format. cobalt can't improve the source audio quality, so choosing a bitrate over 128kbps may inflate the file size with no audible difference. perceived quality may vary by format.", "audio.youtube.dub": "youtube audio track", "audio.youtube.dub.title": "preferred dub language", "audio.youtube.dub.description": "cobalt will use a dubbed audio track for selected language if it's available. if not, original will be used instead.", "youtube.dub.original": "original", "subtitles": "subtitles", "subtitles.title": "preferred subtitle language", "subtitles.description": "cobalt will add subtitles to the downloaded file in the preferred language if they're available.\n\nsome services don't have a language selection, and if that's the case, cobalt will add the only available subtitle track if you have any language selected.", "subtitles.none": "none", "audio.youtube.better_audio": "youtube audio quality", "audio.youtube.better_audio.title": "prefer better quality", "audio.youtube.better_audio.description": "cobalt will try to pick highest quality audio in audio mode. it may not be available depending on youtube's response, current traffic, and server status. custom instances may not support this option.", "audio.tiktok.original": "tiktok", "audio.tiktok.original.title": "download original sound", "audio.tiktok.original.description": "cobalt will download the sound from the video without any changes by the post's author.", "metadata.filename": "filename style", "metadata.filename.classic": "classic", "metadata.filename.basic": "basic", "metadata.filename.pretty": "pretty", "metadata.filename.nerdy": "nerdy", "metadata.filename.description": "filename style will only be used for files tunneled by cobalt. some services don't support filename styles other than classic.", "metadata.filename.preview.video": "Video Title - Video Author", "metadata.filename.preview.audio": "Audio Title - Audio Author", "filename.preview_desc.video": "video file preview", "filename.preview_desc.audio": "audio file preview", "metadata.file": "file metadata", "metadata.disable.title": "disable file metadata", "metadata.disable.description": "title, artist, and other info will not be added to the file.", "saving.title": "saving method", "saving.ask": "ask", "saving.download": "download", "saving.share": "share", "saving.copy": "copy", "saving.description": "preferred way of saving the file or link from cobalt. if preferred method is unavailable or something goes wrong, cobalt will ask you what to do next.", "accessibility.visual": "visual", "accessibility.haptics": "haptics", "accessibility.behavior": "behavior", "accessibility.transparency.title": "reduce visual transparency", "accessibility.transparency.description": "transparency of surfaces will be reduced and all blur effects will be disabled. may also improve ui performance on less powerful devices.", "accessibility.motion.title": "reduce motion", "accessibility.motion.description": "animations and transitions will be disabled whenever possible.", "accessibility.haptics.title": "disable haptics", "accessibility.haptics.description": "all haptic effects will be disabled.", "accessibility.auto_queue.title": "don't open the queue automatically", "accessibility.auto_queue.description": "the processing queue will not be opened automatically whenever a new item is added to it. progress will still be displayed and you will still be able to open it manually.", "language": "language", "language.auto.title": "automatic selection", "language.auto.description": "cobalt will use your browser's default language if translation is available. if not, english will be used instead.", "language.preferred.title": "preferred language", "language.preferred.description": "this language will be used when automatic selection is disabled. any text that isn't translated will be displayed in english.\n\nsome languages use community-sourced translations, they may be inaccurate or incomplete.", "privacy.analytics": "anonymous traffic analytics", "privacy.analytics.title": "don't contribute to analytics", "privacy.analytics.description": "anonymous traffic analytics are needed to get an approximate number of active cobalt users. no identifiable information about you is ever stored. all processed data is anonymized and aggregated.\n\nwe use a self-hosted plausible instance that doesn't use cookies and is fully compliant with GDPR, CCPA, and PECR.", "privacy.analytics.learnmore": "learn more about <PERSON>'s dedication to privacy.", "privacy.tunnel": "tunneling", "privacy.tunnel.title": "always tunnel files", "privacy.tunnel.description": "cobalt will hide your ip address, browser info, and bypass local network restrictions. when enabled, files will also have readable filenames that otherwise would be gibberish.", "advanced.debug": "debug", "advanced.debug.title": "enable features for nerds", "advanced.debug.description": "gives you easy access to app info that can be useful for debugging. enabling this does not affect functionality of cobalt in any way.", "processing.community": "community instances", "processing.enable_custom.title": "use a custom processing server", "processing.enable_custom.description": "cobalt will use a custom processing instance if you choose to. even though cobalt has some security measures in place, we are not responsible for any damages done via a community instance, as we have no control over them.\n\nplease be mindful of what instances you use and make sure they're hosted by people you trust.", "processing.access_key": "instance access key", "processing.access_key.title": "use an instance access key", "processing.access_key.description": "cobalt will use this key to make requests to the processing instance instead of other authentication methods. make sure the instance supports api keys!", "processing.custom_instance.input.alt_text": "custom instance domain", "processing.access_key.input.alt_text": "u-u-i-d access key", "advanced.settings_data": "settings data", "advanced.local_storage": "local storage", "local.saving": "local media processing", "local.saving.description": "when downloading media, remuxing and transcoding will be done on-device instead of the cloud. you'll see detailed progress in the processing queue.\n\ndisabled: local processing will not be used. processing instances can enforce local processing, so this option may not have effect.\npreferred: media that requires extra processing will be downloaded through the processing queue, but the rest of media will be downloaded by your browser's download manager.\nforced: all media will always be proxied and downloaded through the processing queue.\n\nexclusive on-device features are not affected by this setting, they always run locally.", "local.saving.disabled": "disabled", "local.saving.preferred": "preferred", "local.saving.forced": "forced", "local.webcodecs": "webcodecs", "local.webcodecs.title": "use webcodecs for on-device processing", "local.webcodecs.description": "when decoding or encoding files, cobalt will try to use webcodecs. this feature allows for GPU-accelerated processing of media files, meaning that all decoding & encoding will be way faster.\n\navailability and stability of this feature depends on your device's and browser's capabilities. stuff might break or not work properly.", "tabs": "navigation", "tabs.hide_remux": "hide the remux tab", "tabs.hide_remux.description": "if you don't use the remux tool, you can hide it from the navigation bar."}