{"name": "@imput/cobalt-web", "version": "11.2.3", "type": "module", "private": true, "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch"}, "license": "CC-BY-NC-SA-4.0", "engines": {"node": ">=20", "pnpm": ">=9"}, "repository": {"type": "git", "url": "git+https://github.com/imputnet/cobalt.git"}, "bugs": {"url": "https://github.com/imputnet/cobalt/issues"}, "homepage": "https://cobalt.tools/", "devDependencies": {"@eslint/js": "^9.5.0", "@fontsource/ibm-plex-mono": "^5.0.13", "@fontsource/redaction-10": "^5.0.2", "@imput/libav.js-encode-cli": "6.8.7", "@imput/libav.js-remux-cli": "^6.8.7", "@imput/version-info": "workspace:^", "@sveltejs/adapter-static": "^3.0.6", "@sveltejs/kit": "^2.20.7", "@sveltejs/vite-plugin-svelte": "^4.0.0", "@tabler/icons-svelte": "3.6.0", "@types/eslint__js": "^8.42.3", "@types/fluent-ffmpeg": "^2.1.25", "@types/node": "^20.14.10", "@vitejs/plugin-basic-ssl": "^1.1.0", "compare-versions": "^6.1.0", "dotenv": "^16.0.1", "eslint": "^9.16.0", "glob": "^11.0.0", "mdsvex": "^0.11.2", "mime": "^4.0.4", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "svelte-preprocess": "^6.0.2", "svelte-sitemap": "2.6.0", "sveltekit-i18n": "^2.4.2", "ts-deepmerge": "^7.0.1", "tslib": "^2.4.1", "turnstile-types": "^1.2.2", "typescript": "^5.5.0", "typescript-eslint": "^8.18.0", "vite": "^5.4.4"}}